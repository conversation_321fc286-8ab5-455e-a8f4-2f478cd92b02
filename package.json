{"name": "zhb2c-new", "version": "1.0.0", "description": "深航新项目", "main": "index.js", "scripts": {"dev": "npm run build && npm run default", "default": "./node_modules/gulp/bin/gulp.js default", "build": "./node_modules/gulp/bin/gulp.js build", "pack": "./node_modules/gulp/bin/gulp.js pack", "format": "prettier --write \"src/**/*.{html,js,css,less,ejs}\"", "format:check": "prettier --check \"src/**/*.{html,js,css,less,ejs}\"", "prepare": "husky"}, "lint-staged": {"src/**/*.{ejs,js,css,less}": ["prettier --write"], "src/**/*.html": ["prettier --write"]}, "author": "zjjing", "license": "ISC", "devDependencies": {"browser-sync": "^3.0.3", "ejs-json": "^1.0.4", "gulp": "^4.0.2", "gulp-clean": "^0.4.0", "gulp-concat": "^2.6.1", "gulp-less": "^5.0.0", "husky": "^9.1.7", "less-plugin-autoprefix": "^2.0.0", "lint-staged": "^16.1.2", "prettier": "^2.8.8", "prettier-plugin-ejs": "^1.0.3", "stream-combiner2": "^1.1.1"}, "dependencies": {"gulp-ejs": "^5.1.0"}}