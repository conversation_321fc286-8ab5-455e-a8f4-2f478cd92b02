/**
 * 乘客表单模块 JavaScript
 * 处理表单交互、验证和数据提交
 */

const idCard = value => {
  // const v: string = type ? DOC_MAP[type] : '身份证';    // v 此处未使用 提交代码过不去 先注掉
  if (!value) return `请输入有效证件号码`;
  let is =
    /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9Xx])$/.test(
      value
    );
  if (is) {
    if (value.length === 18) {
      const idCardWi = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]; //将前17位加权因子保存在数组里
      const idCardY = [1, 0, 10, 9, 8, 7, 6, 5, 4, 3, 2]; //这是除以11后，可能产生的11位余数、验证码，也保存成数组
      let idCardWiSum = 0; //用来保存前17位各自乖以加权因子后的总和
      for (let i = 0; i < 17; i++) {
        idCardWiSum += parseInt(value.substring(i, i + 1)) * idCardWi[i];
      }
      const idCardMod = idCardWiSum % 11; //计算出校验码所在数组的位置
      const idCardLast = value.substring(17); //得到最后一位身份证号码
      //如果等于2，则说明校验码是10，身份证号码最后一位应该是X
      if (idCardMod === 2) {
        is = idCardLast === 'X';
      } else {
        //用计算出的验证码与最后一位身份证号码匹配，如果一致，说明通过，否则是无效的身份证号码
        is = parseInt(idCardLast) === idCardY[idCardMod];
      }
    }
  }
  return is ? '' : `请输入有效证件号码`;
  // return is ? '' : `请输入有效证件信息`;
};

/**
 * 通用输入校验函数
 * 校验成功返回空字符串 ""，失败返回错误信息字符串。
 *
 * @param {'phone' | 'email' | 'idCard', 'passport'} type - 校验类型，目前支持 'phone' 和 'email'。
 * @param {string} value - 要校验的字符串值。
 * @returns {string} 校验失败时的错误信息，如果校验成功则返回空字符串。
 */
function validateInput(type, value) {
  if (!value) {
    // 统一处理空值情况
    switch (type) {
      case 'phone':
        return '手机号不能为空';
      case 'email':
        return '邮箱不能为空';
      case 'idCard':
        return '身份证不能为空';
      default:
        return '值不能为空'; // 理论上不会走到这里，因为type已限定
    }
  }

  switch (type) {
    case 'phone':
      // 中国大陆手机号正则：以1开头，第二位是3-9，后面跟着9位数字
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(value)) {
        return '手机号格式不正确';
      }
      break; // 校验通过，跳出 switch

    case 'email':
      // 相对宽松的邮箱正则，能匹配大多数合法邮箱格式
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailRegex.test(value)) {
        return '邮箱格式不正确';
      }
      break; // 校验通过，跳出 switch

    case 'idCard':
      return idCard(value);
    case 'passport':
      return /^[A-Za-z0-9]{5,15}$/.test(value) ? '' :'请输入正确的护照格式';

    default:
      // 如果传入了未知的类型 (尽管 TypeScript 会在编译时阻止) 这种校验默认只有必填项校验
      console.warn(`validateInput: 未知的校验类型 "${type}"`);
      return '';
  }

  return ''; // 所有校验通过，返回空字符串
}


// 出生日期验证
function isValidBirthDate(birthDate, fieldId) {
  const today = new Date()
  const birth = new Date(birthDate)
  const age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()
  const dayDiff = today.getDate() - birth.getDate()

  let actualAge = age
  if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
    actualAge--
  }

  // 根据乘客类型验证年龄
  if (fieldId.includes('adult')) {
    return actualAge >= 18
  } else if (fieldId.includes('child')) {
    return actualAge >= 2 && actualAge < 12
  } else if (fieldId.includes('infant')) {
    return actualAge >= 0 && actualAge < 2
  }

  return true
}


// 初始化表单验证  带sz的相关form组件校验
// 验证单个字段
function validateField(field) {
  // console.log('event', event);
  // const field = event.target
  const fieldGroup = field.closest('.form-item');
  const formType = $(field).data('type');
  const defaultErrorMsg = $(field).data('errorMsg');
  const fieldValue = field.value.trim();
  const isRequired = $(field).data('required');

  // console.log('formType',formType);
  // 清除之前的错误状态
  clearFieldError({ target: field });

  // console.log('fieldValue',fieldValue,isRequired);
  // 必填字段验证
  if (isRequired && !fieldValue) {
    showFieldError(fieldGroup, defaultErrorMsg);
    return false;
  }

  if(formType){
    const msg = validateInput(formType, fieldValue);
    if (isRequired && msg) {
      showFieldError(fieldGroup, msg);
      return false;
    }
  }


  // 验证通过，显示成功状态
  showFieldSuccess(fieldGroup);
  return true;
}

// 清除字段错误状态
function clearFieldError(event) {
  const field = event.target;
  const fieldGroup = field.closest('.form-item');
  fieldGroup.classList.remove('error');
  fieldGroup.classList.add('success');

  // console.log('11111clear', fieldGroup);
  // 移除错误提示
  const errorTip = fieldGroup.querySelector('.error-tips');
  if (errorTip) {
    errorTip.style.display = 'none';
  }
}

// 显示字段错误
function showFieldError(fieldGroup, message) {
  fieldGroup.classList.add('error');
  fieldGroup.classList.remove('success');
  // 添加错误提示
  const existingTip = fieldGroup.querySelector('.error-tips');
  // console.log('existingTip', existingTip);
  if (existingTip) {
    existingTip.style.display = 'block';
    $(existingTip).find('.error-content').text(message);
  }
}

// 显示字段成功状态
function showFieldSuccess(fieldGroup) {
  fieldGroup.classList.add('success');
  fieldGroup.classList.remove('error');

  // 移除错误提示
  const errorTip = fieldGroup.querySelector('.error-tip');
  if (errorTip) {
    errorTip.remove();
  }
}

/**
 * 从指定的 jQuery 父容器中获取所有表单元素的值。
 *
 * @param {jQuery} parentContainer jQuery DOM 对象，包含表单元素。
 * @returns {object} 包含所有表单元素值的对象。
 */
function getFormValues(parentContainer) {
  // console.log('ffff',parentContainer);
  if (!parentContainer || parentContainer.length === 0) {
    console.error('Error: Parent container not a valid jQuery object or element not found.');
    return {};
  }

  const formData = {};

  // 1. 查找父容器下的所有 input, select, textarea 元素
  const formElements = parentContainer.find('input, select, textarea');

  // console.log('formElements',formElements);
  // 2. 遍历所有找到的元素
  formElements.each(function() {
    const element = $(this);
    const name = element.data('name');

    // 如果没有 name 属性，则跳过
    if (!name) {
      return;
    }

    //因为用的自定义的select  自定义的select用的input实现的。
    // 所以这里用另外一个属性来啊判断是普通input框还是select框
    const type = element.attr('data-inputType');

    // 3. 根据元素类型处理值
    switch (element.prop('tagName')) {
      case 'INPUT':
        switch (type) {
          case 'select':
            //select框
            // 单选框，只记录选中的那个
            formData[name] = element.data('value');
            break;
          default:
            //input 框
            // 其他类型（text, password, etc.）直接赋值
            formData[name] = element.val();
            break;
        }
        break;

      case 'SELECT':
        // 多选 select
        if (element.prop('multiple')) {
          formData[name] = element.val() || [];
        } else {
          // 单选 select
          formData[name] = element.val();
        }
        break;

      case 'TEXTAREA':
        // textarea
        formData[name] = element.val();
        break;
    }
  });

  return formData;
}


/**
 * 验证当前页面所有的form组件
 * @returns {boolean}
 */
function validateAllForms(){

  const allInputs = document.querySelectorAll('input[data-required="true"]');
  allInputs.forEach((e)=>{
    validateField(e);
  });
  const formItems = $('.form-item');
  let isError = false;
  for (let i = 0; i < formItems.length; i++) {
    const  formItem = formItems[i];
    // console.log('formItem',formItem);
    if($(formItem).hasClass('error')){
      isError = true;
      break;
    }
  }
  return !isError;
}