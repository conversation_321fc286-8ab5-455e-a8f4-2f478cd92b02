<%
    //国际段类型，  1.非美航段。2.美国航段-非美籍 3.美国航段-美籍
    const locationType = typeof type !== 'undefined' ? type : 1;

 %>


<div class="passenger-form-content">
    <div class="form-row">

        <%- include('../../../components/sz-input/input',{
            label:'First Name',
            tipText:'123',
            required: true,
            formName:'firstName',
        }) %>

        <%- include('../../../components/sz-input/input',{
            label:'Last Name',
            errorMsg:'请输入',
            required:true,
            formName:'lastName',
        }) %>
    </div>

    <div class="form-row">
        <%- include('../../../components/sz-select/index',{
            label:'Gender',
            tipText:'8384sele',
            required:true,
            formName:'gender',
            options:[
                {
                    label:'Male',
                    value:'male',
                },
                {
                    label:'Female',
                    value:'female',
                },
            ],
        }) %>

        <%- include('../../../components/sz-select/index',{
            label:'Nationality',
            required: true,
            formName:'nationality',
            options:[
                {
                    label:'中国',
                    value:'CN'
                },
                {
                    label:'美国',
                    value:'US',
                },
            ],
        }) %>
    </div>

    <div class="form-row">

        <%- include('../../../components/sz-datepicker/index',{
            label:'Date of birth',
            tipText:'datepicketips',
            required:true,
            formName:'dateBirth',
        }) %>
        <%- include('../../../components/sz-select/index',{
            label:'Document Type',
            required: true,
            className:'doc-type',
            formName:'docType',
            options:[
                {
                    label:'护照',
                    value:'passport',
                },
                {
                    label:'身份证',
                    value:'idCard',
                },
            ],
        }) %>
    </div>

    <div class="form-row">
        <%- include('../../../components/sz-input/input',{
            label:'Document Number',
            errorMsg:'请输入',
            className:'doc-num',
            required: true,
            formName:'docNumber',
            type:"idCard",
        }) %>

        <%- include('../../../components/sz-select/index',{
            label:'issuing country/Region',
            required: true,
            formName:'region',
                options:[
                {
                    label:'北京',
                    value:'bj',
                },
                {
                    label:'上海',
                    value:'sh',
                }
            ],
        }) %>
    </div>

    <div class="form-row">

        <%- include('../../../components/sz-select/index',{
            label:'country of residence',
            required: true,
            formName:'country',
            options:[
                    {
                        label:'北京',
                        value:'bj',
                    },
                    {
                        label:'上海',
                        value:'sh',
                    }
            ],
        }) %>

        <%- include('../../../components/sz-datepicker/index',{
            label:'Expiration Type',
            required:true,
            formName:'expirationType',
        }) %>
    </div>
    <div class="form-row">
        <%- include('../../../components/sz-select/index',{
            label:'Are there any transit passengers',
            required: true,
            formName:'arePassenger',
            options:[
                {
                    label:'北京',
                    value:'bj',
                },
                {
                    label:'上海',
                    value:'sh',
                }
            ],
        }) %>
    </div>


    <% if(locationType===2) {%>
        <div class="usa-segment-u">
            <div class="form-row">
                <%- include('../../../components/sz-select/index',{
                    label:'Country',
                    required: true,
                    formName:'sCountry',
                    options:[
                        {
                            label:'USA',
                            value:'usa',
                        },
                        {
                            label:'上海',
                            value:'sh',
                        }
                    ],
                }) %>
                <%- include('../../../components/sz-select/index',{
                    label:'Continet\Province',
                    required: true,
                    formName:'sContinet',
                    options:[
                        {
                            label:'USA',
                            value:'usa',
                        },
                        {
                            label:'上海',
                            value:'sh',
                        }
                    ],
                }) %>
            </div>
            <div class="form-row">
                <%- include('../../../components/sz-select/index',{
                    label:'Market',
                    required: true,
                    formName:'sMarket',
                    options:[
                        {
                            label:'北京',
                            value:'bj',
                        },
                        {
                            label:'上海',
                            value:'sh',
                        }
                    ],
                }) %>
            </div>
            <div class="form-row">
                <%- include('../../../components/sz-input/input',{
                    label:'Street Address',
                    required: true,
                    formName:'sStreetAddress',
                    className:'street-address',
                    tipText:'123',
                }) %>
            </div>
            <div class="form-row">
                <%- include('../../../components/sz-input/input',{
                    label:'Zip code',
                    required: true,
                    formName:'sZipCode',
                    tipText:'123',
                }) %>

                <%- include('../../../components/sz-input/input',{
                    label:'Phone',
                    required: true,
                    formName:'sPhone',
                    tipText:'123',
                    type:'phone',
                }) %>

            </div>
        </div>
    <%}%>

    <% if(locationType===3) {%>
        <div class="usa-segment-u">
            <div class="form-row">
                <%- include('../../../components/sz-select/index',{
                    label:'Id type',
                    required: true,
                    formName:'idType',
                    options:[
                        {
                            label:'USA',
                            value:'usa',
                        },
                        {
                            label:'上海',
                            value:'sh',
                        }
                    ],
                }) %>

                <%- include('../../../components/sz-input/input',{
                    label:'ID NO',
                    required:true,
                    formName:'idNo',
                    tipText:'123',
                }) %>
            </div>
        </div>
    <%}%>
</div>

