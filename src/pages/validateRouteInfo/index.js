$(function () {

    function initCollapseToggle() {
        const toggleButtons = document.querySelectorAll('.expand-collapse-btn')

        toggleButtons.forEach((button) => {
            button.addEventListener('click', function () {
                const formGroup = this.closest('.passenger-form-group')
                if (formGroup.classList.contains('collapsed')) {
                    formGroup.classList.remove('collapsed')
                } else {
                    formGroup.classList.add('collapsed')
                }
            })
        })


        $(document).on('click', '.validate-info #flight-btn', function (event) {
            const flightDetail = $(this).closest('.flight-segment').find('.flight-detail');
            const equityInfo = $(this).closest('.flight-segment').find('.equity-info');
            const refundInfo = $(this).closest('.flight-segment').find('.refund-info');

            refundInfo.removeClass('show');
            equityInfo.removeClass('show');
            flightDetail.toggleClass('show');

            $(this).closest('.flight-segment').find('#refund-btn .arrow-up').removeClass('active');
            $(this).closest('.flight-segment').find('#equity-btn .arrow-up').removeClass('active');
            $(this).find('.arrow-up').toggleClass('active');
            event.stopPropagation();
        })

        $(document).on('click', '.validate-info .flight-summary', function () {
            $('.flight-info-header .flight-detail').removeClass('show');
            $('.flight-info-header .arrow-up').removeClass('active');
        });

        $(document).on('click', '#submit-btn', function () {
            // console.log('22222222222submit');

            const allInputs = document.querySelectorAll('input[data-required="true"]');
            allInputs.forEach((e) => {
                validateField(e);
            });
            if (validateAllForms()) {
                const formValues1 = getFormValues($('#psg-1'));
                const formValues2 = getFormValues($('#psg-2'));
                const formValues3 = getFormValues($('#psg-3'));
                console.log('所有form的值', formValues1, formValues2, formValues3);
            }else{
                console.log('还有校验未过');
            }

        });


        $(document).on('change', '.doc-type input', function () {
            console.log('tttt', this.dataset.value);
            const curValue = this.dataset.value;
            // const curValue = $(this).find('input').data('value');

            //给docNum重新的校验规则类型
            const docNum = $(this).closest('.passenger-form-content').find('.doc-num input');
            docNum.data('type', curValue);

        })
    }

    initCollapseToggle();
})