@import '../../less/variables.less';
@import '../../less/mediaMixin.less';

.vri {
  // 航班信息头部
  .flight-info-header {
    background: @gray-0;
    //overflow: hidden;
    //margin-bottom: 20px;
    margin-top: 20px;


    .flight-segment{
      margin-bottom: 20px;
      position: relative;
    }

    .flight-summary {
      padding: 20px;
      border-radius: 8px;
      border: 1px solid @gray-2;

      //border-bottom: 1px solid @gray-2;


      .trip-type {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 20px;


        .trip-badge {
          background: @orange-3;
          color: @gray-0;
          padding: 2px 10px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
          text-transform: uppercase;

          .screenMobile({
            padding: 5px 12px;
            font-size: 11px;
          });
        }

        .flight-date {
          font-size: 14px;
          color: @gray-dark;
          //font-weight: 500;

          .screenMobile({
            font-size: 13px;
          });
        }
      }

      .flight-route-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 20px;

        .screenMobile({
          margin-bottom: 16px;
        });

        .departure,
        .arrival {
          flex: 1;

          .city-name {
            font-size: 28px;
            font-weight: 600;
            color: @gray-5;
            margin-bottom: 4px;

            .screenMobile({
              font-size: 20px;
            });
          }

          .flight-time {
            font-size: 18px;
            color: @gray-dark;
            //font-weight: 500;

            .screenMobile({
              font-size: 16px;
            });
          }
        }

        .arrival {
          text-align: right;
        }

        .route-divider-header {
          display: flex;
          justify-content: center;
          align-items: center;
          flex: 1;
          margin: 0 20px;

          .screenMobile({
            margin: 0 12px;
          });

          .route-line {
            flex: 1;
            height: 1px;
            background: @gray-3;
          }

          .flight-icon {
            //width: 22px;
            font-size: 22px;
            margin: 0 20px;
            color: @gray-3;
            //margin-top: -10px;

            //transform: rotate(90deg);

            .screenMobile({
              font-size: 18px;
            });
          }
        }
      }

      .flight-options {
        display: flex;
        gap: 24px;
        justify-content: flex-end;

        .screenMobile({
          gap: 6px;
          align-items: stretch;

        });

        .option-btn {
          background: none;
          border: none;
          color: @brand-2;
          font-size: 14px;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 6px 12px;
          border-radius: 4px;
          transition: all 0.3s ease;

          .screenMobile({
            font-size: 12px;
          });


          &:hover {
            background: @gray-f5;
            //color: @gray-dark;
          }

          .arrow-up {
            font-size: 11px;
            transform: rotate(90deg);

            &.active{
              transform: rotate(0deg);
            }
          }

          &:hover .arrow-down {
            transform: rotate(180deg);
          }
        }
      }
    }


    //退改签规则和权益
    .refund-info, .equity-info, .flight-detail {
      position: absolute;
      top: 100%;
      display: none;
      width: 100%;
      align-items: center;
      gap: 10px;
      padding: 20px;
      border: 1px solid @brand-1;
      border-radius: 0 0 8px 8px;
      background: @gray-0;
      z-index: 10;

      .screenMobile({
        padding: 16px 12px;
      });

      .tab-tit {
        height: 34px;
        line-height: 30px;
        background: #F4F6F9;
        color: #3D3D3D;
        text-align: center;
        border-radius: 8px 8px 0 0;
        font-size: 16px;
        font-weight: 500;

        .rectangle {
          width: 21px;
          height: 3px;
          margin: 0 auto;
          background: @brand-2;
          border-radius: 0 0 8px 8px;
        }
      }
    }

    .refund-info.show, .equity-info.show {
      display: flex;
    }

    .flight-detail.show {
      display: block;
    }

    //退改签规则
    .refund-info {
      //border-top: none;


      .screenMobile({
        flex-direction: column;
      });

      .ticket-refund, .ticket-changes {
        width: 50%;
        box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);

        .screenMobile({
          width: 100%;
        });
      }

      .sub-tit {
        padding: 10px 20px;
        color: @gray-5;
        font-size: 16px;
        font-weight: 500;
      }

      .sub-item {
        display: flex;
        justify-content: space-between;
        padding: 10px 20px;
        font-size: 14px;

        .sub-value {
          color: @orange-3;
        }
      }
    }


    //权益
    .equity-info {

      .luggage, .base-mileage, .bonus-mileage {
        flex: 1;
        text-align: center;
      }

      .equity-con {
        padding: 10px 20px;
        font-size: 16px;
        //
        box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
      }

      .tab-tit {
        font-weight: normal;
      }
    }

    .flight-detail {
      width: 100%;
      //display: flex;
      font-size: 16px;
      color: @gray-5;

      .screenMobile({
        font-size: 12px;
      });

      .flex {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 6px 0;
      }

      .flight-date {
        //margin-bottom: 10px;

        .dep-time, .arrival-time {

          .screenMobile({
            display: flex;
            flex-direction: column;
          });

          .time {
            font-size: 40px;
            font-weight: bold;
            .screenMobile({
              font-size: 30px;
            });
          }

          .date {
            margin-left: 4px;
            font-size: 14px;
            .screenMobile({
              font-size: 12px;
              margin-left: 0;
            });
          }
        }

        .arrival-time {

          .screenMobile({
            flex-direction: column-reverse;
          });

          .date {
            margin-right: 4px;
            .screenMobile({
              margin-right: 0;
            });
          }
        }

        .flight-line {
          height: 1px;
          width: 100px;
          display: inline-block;
          margin: 0 12px;
          background: @gray-1;
          .screenMobile({
            width: 36px;
            margin: 0 6px;
          });
        }

        .flight-duration {
          text-align: center;
          color: @sub-4;
          font-size: 14px;

          .screenMobile({
            font-size: 12px;
          });

          .duration-time {
            text-align: center;
            padding: 1px 6px;
            background: #F8F1E5;
            border-radius: 8px;
          }

          .stop-t {
            margin-top: 6px;

            &:last-child {
              margin-top: 2px;
            }
          }
        }
      }


      .depart, .arrival {
        color: @gray-5;

        .screenMobile({
          max-width: 50%;
          white-space: wrap;
        })
      }

      .arrival {
        .screenMobile({
          text-align: right;
        })

      }

      .cabin-item {
        .screenMobile({
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          text-align: right;
        })
      }

      .flight-number {
        color: @sub-4;
      }

      .flight-type {
        padding: 1px 4px;
        background: @red-1;
        color: @sub-4;
      }

      .equity-name {
        margin-right: 10px;
        color: @brand-2;
      }


      .transit-line {
        display: flex;
        justify-content: center;
        height: 1px;
        width: 100%;
        margin: 36px 0 28px 0;
        background: @gray-3;

        .t-line {

        }

        .transit-s-text {
          height: 30px;
          line-height: 30px;
          margin-top: -15px;
          padding: 0 12px;
          border-radius: 8px;
          background: @gray-3;
          color: @gray-0;
        }

      }

      img {
        vertical-align: top;
      }
    }

  }


  .passenger-info-title{
    display: flex;
    justify-content: flex-end;
    margin: 20px 0;
    font-size: 24px;
    font-weight: normal;
  }

  // 乘客表单组
  .passenger-form-group {
    background: @gray-0;
    //border: 1px solid @gray-2;
    border-radius: 8px;
    overflow: hidden;

    // 乘客表单头部
    .passenger-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      //background: @gray-f7;
      //border-bottom: 1px solid @gray-2;

      .passenger-icon-wrapper {
        display: flex;
        align-items: center;
        gap: 12px;

        .passenger-icon {
          font-size: 14px;
          width: 22px;
          height: 22px;
          color: @gray-e1;
          text-align: center;
          line-height: 22px;
          background: @orange-3;
          border-radius: 4px;

          .screenMobile({
            font-size: 20px;
          });
        }

        .passenger-icon-t {
          vertical-align: top;
        }

        .passenger-label {
          font-size: 28px;
          font-weight: 600;
          color: @gray-5;

          .passenger-type{
            font-weight: normal;
            font-size: 24px;
            color: @sub-4;
          }

          .screenMobile({
            font-size: 16px;
          });
        }

        .passenger-choose {
          font-size: 16px;
          color: @gray-3;
          cursor: pointer;
          //font-style: italic;

          .screenMobile({
            font-size: 12px;
          });

          .choose-frequently-btn:hover {
            text-decoration: underline;
          }
        }
      }

      .expand-collapse-btn {
        cursor: pointer;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid @gray-lighter;
        border-radius: 100%;
        background: @gray-0;
        transition: all 0.3s ease;

        &:hover {
          background: @gray-f5;
          border-color: @gray-light;
        }

        .collapse-text {
          font-size: 18px;
          font-weight: bold;
          color: @gray-dark;

          img {
            height: 12px;
            width: 12px;
            display: inline-block;
          }
        }
      }
    }

    // 乘客表单内容
    .passenger-form-content {
      padding: 24px 20px;

      .screenMobile({
        padding: 16px 15px;
      });

      .usa-segment-u{
        border-top: 1px solid @brand-1;
        padding-top: 30px;
        margin-top: 30px;
      }

      .street-address{
        width: 100% ;
      }
    }

    // 表单行
    .form-row {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;

      .screenMobile({
        flex-direction: column;
        gap: 15px;
        margin-bottom: 15px;
      });

      &:last-child {
        margin-bottom: 0;
      }
    }

    // 表单组（半宽）
    .form-item{
      width: 50%;
      padding-bottom: 10px;

      .screenMobile({
        width: 100%;
      });

    }

    .form-contact {
      display: flex;
      width: 50%;

      .screenMobile({
        width: 100%;
      });

      .contact-type{
        width: 35%;

      }

      .contact-number{
        width: 65%;
      }
      .label-wrap {
        width: 300%;
      }

      .input-single.input-group.error .text {
        border: none;
      }
    }
  }

  .passenger-form-group.collapsed {
    .passenger-form-content {
      display: none;
    }

    .passenger-header .expand-collapse-btn .collapse-text {
      transform: rotate(-90deg);
    }
  }


  .next-confirm{
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    margin-right: 12px;

    .submit-btn{
      width: 230px;
      height: 50px;
      font-size: 20px;
      color: @gray-0;
      background: @brand-1;
      border-radius: 8px;
    }
  }




  .input-single .label-wrap{
    overflow: visible;
    .bubble-info{

      width: 200px;
      left: 0;
      margin-left: -30px;
      .screenMobile({
        width: 300px;
        white-space: wrap;
        margin-left: 0;
      });
      .screenMobile({
        width: 300px;
        white-space: wrap;
        margin-left: 0;
      });

    }
  }

}