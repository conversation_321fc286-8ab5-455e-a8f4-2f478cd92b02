<div class="pc-form-tab-wrap" role="tab">
  <div class="tabs-wrap" role="tablist">
    <a
      href="javascript:"
      role="option"
      id="tab1"
      onclick="toggleIndex(1)"
      class="tab"
    >
      Flights
    </a>
    <a
      href="javascript:"
      role="option"
      id="tab2"
      onclick="toggleIndex(2)"
      class="tab"
    >
      Seat selection check-in
    </a>
    <a
      href="javascript:"
      role="option"
      id="tab3"
      onclick="toggleIndex(3)"
      class="tab"
    >
      My booking
    </a>
    <a
      href="javascript:"
      role="option"
      id="tab4"
      onclick="toggleIndex(4)"
      class="tab"
    >
      Flight Status
    </a>
  </div>

  <section role="tabpanel" aria-labelledby="tab1" class="plane" id="plane1">
    <%- include('./flights') %>
  </section>

  <section role="tabpanel" class="plane" id="plane2" aria-labelledby="tab2">
    <div class="book-seat-check">
      <div
        aria-description="seat selection check-in"
        class="form-seat-check tab-target"
      >
        <div class="form-tab clearfix">
          <div data-tabBegin class="no-style tab active" role="button">
            Seat selection check-in
          </div>
          <div class="no-style tab" role="button">Record Search</div>
        </div>
        <div class="form-wrap">
          <div class="form-items active">
            <form class="form-table">
              <div class="form-seat-flex">
                <div class="input-wrap">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="“idCard"
                        >Please enter your ID card number</label
                      >
                    </div>
                    <input
                      type="text"
                      id="idCard"
                      class="text"
                      placeholder="Please fill in the input"
                      title="Please fill in the input"
                      value=""
                      aria-required="true"
                    />
                  </div>
                </div>
                <div class="input-wrap">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="lastName">Last name/Surnname</label>
                    </div>
                    <input
                      type="text"
                      id="lastName"
                      class="text"
                      placeholder="Please fill in the input"
                      title="Please fill in the input"
                      value=""
                      aria-required="true"
                    />
                  </div>
                </div>
                <div class="input-wrap">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="firstName">First Name</label>
                    </div>
                    <input
                      type="text"
                      id="firstName"
                      class="text"
                      placeholder="Please fill in the input"
                      title="Please fill in the input"
                      value=""
                      aria-required="true"
                    />
                  </div>
                </div>
                <div class="input-wrap">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="flightNo"
                        >Please input the flight number</label
                      >
                    </div>
                    <input
                      type="text"
                      id="flightNo"
                      class="text"
                      placeholder="Please fill in the input"
                      title="Please fill in the input"
                      value=""
                      aria-required="true"
                    />
                  </div>
                </div>
                <div class="input-wrap calendar-group">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="single-date" class="title">Departure</label>
                    </div>
                    <input
                      type="text"
                      class="text calendar-input calendar-input-disabled-before depart-input calendar-flag"
                      id="single-date"
                      placeholder="Please choose"
                      aria-required="true"
                      aria-label="calendar，please press enter to choose date"
                    />
                    <i class="icon-zh icon-zh-calendar calendar"></i>
                  </div>
                </div>
              </div>
              <div class="check-read">
                <label
                  class="checkbox-group is-select"
                  aria-checked="false"
                  role="checkbox"
                >
                  <span class="checkbox"
                    ><input type="checkbox" /><i
                      class="icon-zh icon-zh-success-yuan"
                    ></i
                  ></span>
                </label>
                <p>
                  Agree to use
                  <a href="javascript:" class="link"
                    >Online Check in Agreement and Notice for Passengers
                    Carrying Dangerous Goods</a
                  >
                </p>
              </div>
              <ul class="warm-tips">
                <li class="title">Gentle hint:</li>
                <li>
                  • The seat selection & check-in function is only for adults
                  and child passengers accompanied by adults; after seat
                  pre-selection, passengers with babies or applying for special
                  services should go to the manual check-in counter of Shenzhen
                  Airlines 2 hours before the flight departure to check in.
                </li>
                <li>
                  • Please select seats and check in at official channels of
                  Shenzhen Airlines.
                </li>
              </ul>
              <div class="btn-box text-right">
                <a
                  role="button"
                  data-tabEnd
                  href="javascript:"
                  aria-label="button Seat selection check-in"
                  class="btn"
                  >Seat selection check-in</a
                >
              </div>
            </form>
          </div>
          <div class="form-items">
            <form class="form-table">
              <div class="form-seat-flex">
                <div class="input-wrap">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="“idCard"
                        >Please enter your ID card number</label
                      >
                    </div>
                    <input
                      type="text"
                      id="idCard"
                      class="text"
                      placeholder="Please fill in the input"
                      title="Please fill in the input"
                      value=""
                      aria-required="true"
                    />
                  </div>
                </div>
                <div class="input-wrap">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="firstName">First Name</label>
                    </div>
                    <input
                      type="text"
                      id="firstName"
                      class="text"
                      placeholder="Please fill in the input"
                      title="Please fill in the input"
                      value=""
                      aria-required="true"
                    />
                  </div>
                </div>
                <div class="input-wrap">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="lastName">Last name/Surnname</label>
                    </div>
                    <input
                      type="text"
                      id="lastName"
                      class="text"
                      placeholder="Please fill in the input"
                      title="Please fill in the input"
                      value=""
                      aria-required="true"
                    />
                  </div>
                </div>
                <div class="input-wrap">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="flightNo"
                        >Please input the flight number</label
                      >
                    </div>
                    <input
                      type="text"
                      id="flightNo"
                      class="text"
                      placeholder="Please fill in the input"
                      title="Please fill in the input"
                      value=""
                      aria-required="true"
                    />
                  </div>
                </div>
                <div class="input-wrap calendar-group">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="single-date" class="title">Departure</label>
                    </div>
                    <input
                      type="text"
                      class="text calendar-input calendar-input-disabled-before depart-input calendar-flag"
                      id="single-date"
                      placeholder="Please choose"
                      aria-required="true"
                      aria-label="calendar，please press enter to choose date"
                    />
                    <i class="icon-zh icon-zh-calendar calendar"></i>
                  </div>
                </div>
              </div>
              <div class="check-read">
                <label
                  class="checkbox-group is-select"
                  aria-checked="false"
                  role="checkbox"
                >
                  <span class="checkbox"
                    ><input type="checkbox" /><i
                      class="icon-zh icon-zh-success-yuan"
                    ></i
                  ></span>
                </label>
                <p>
                  Agree to use
                  <a href="javascript:" class="link"
                    >Online Check in Agreement and Notice for Passengers
                    Carrying Dangerous Goods</a
                  >
                </p>
              </div>
              <ul class="warm-tips">
                <li class="title">Gentle hint:</li>
                <li>
                  • The seat selection & check-in function is only for adults
                  and child passengers accompanied by adults; after seat
                  pre-selection, passengers with babies or applying for special
                  services should go to the manual check-in counter of Shenzhen
                  Airlines 2 hours before the flight departure to check in.
                </li>
                <li>
                  • Please select seats and check in at official channels of
                  Shenzhen Airlines.
                </li>
              </ul>
              <div class="btn-box text-right">
                <a href="javascript:" role="button" class="btn">query</a>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section role="tabpanel" class="plane" id="plane3" aria-labelledby="ta3">
    <div class="book-plane-inner">
      <form
        role="form"
        aria-description="my booking form"
        class="form-items-wrap"
      >
        <div class="input-group input-single filled">
          <div class="label-wrap">
            <label for="3-1-input">Type</label>
          </div>
          <input
            type="text"
            id="3-1-input"
            data-tabBegin
            readonly
            onclick="openDropdown(this)"
            class="text"
            placeholder="Please select the departure"
            value="hello world1"
            aria-required="true"
            readonly
          />
          <i
            class="icon-zh icon-zh-right-type1 dropdown-icon"
            alt="select icon"
          ></i>
        </div>

        <div class="input-group input-single">
          <div class="label-wrap">
            <label for="3-2-input">Order NO./TicketNO.</label>
          </div>

          <input
            id="3-2-input"
            aria-required="true"
            class="text"
            placeholder="please input"
            title="please input"
          />
        </div>

        <div class="input-group input-single">
          <div class="label-wrap">
            <label for="3-3-input">Frist Name</label>
          </div>

          <input
            id="3-3-input"
            aria-required="true"
            class="text"
            placeholder="please input"
            title="please input"
          />
        </div>
        <div class="input-group input-single">
          <div class="label-wrap">
            <label for="3-4-input">Last name/Surname</label>
          </div>

          <input
            id="3-4-input"
            aria-required="true"
            class="text"
            placeholder="please input"
            title="please input"
          />
        </div>
        <div class="input-group input-single">
          <div class="label-wrap">
            <label for="3-5-input">Please input the flight number</label>
          </div>

          <input
            id="3-5-input"
            aria-required="true"
            class="text"
            placeholder="please input"
            title="please input"
          />
        </div>
        <div
          class="depart-input input-group input-wrap calendar-group input-single"
          id="picker1"
        >
          <div class="label-wrap">
            <label for="3-6-input">Departure</label>
          </div>

          <input
            id="3-6-input"
            aria-required="true"
            class="text calendar-input calendar-input-disabled-before depart-input calendar-flag"
            placeholder="please choose"
          />
          <i class="icon-zh icon-zh-calendar calendar"></i>
        </div>
      </form>

      <div class="btn-box">
        <a
          role="button"
          data-tabEnd
          href="javascript:"
          aria-label="search button"
          class="submit-btn __main-button"
          >Search</a
        >
      </div>
    </div>
  </section>

  <section role="tabpanel" class="plane" id="plane4" aria-labelledby="tab4">
    <div class="status-plane-inner">
      <div class="inner-tab-wrap" role="radiogroup">
        <a
          href="javascript:"
          role="option"
          data-tabBegin
          id="plane4InnerBtn1"
          onclick="togglePlane4InnerTab(0)"
          onfocusin="togglePlane4InnerTab(0)"
          class="no-style inner-tab-item"
        >
          Buy route
        </a>
        <a
          href="javascript:"
          role="option"
          id="plane4InnerBtn2"
          onclick="togglePlane4InnerTab(1)"
          onfocusin="togglePlane4InnerTab(1)"
          class="no-style inner-tab-item"
        >
          By flight number
        </a>
      </div>

      <div id="tab1plane" class="plane-4-inner-plane">
        <form
          class="form-items-wrap form-items-wrap-icon"
          role="form"
          aria-description="Buy route form"
        >
          <div class="input-group input-single city-origin">
            <div class="label-wrap"><label for="city-from">From</label></div>
            <input
              type="text"
              id="flightTrendsSearchdepartAear"
              class="text city-component"
              placeholder="Please select the departure"
              value=""
              data-citycode=""
              aria-required="true"
            />
          </div>
          <span
            onclick="replacementValue('flightTrendsSearchdepartAear', 'flightTrendsSearchArriveArea')"
            class="cursor-pointer icon-repeatedly icon-zh icon-zh-change-flight change"
          >
          </span>
          <div class="input-group input-single city-origin">
            <div class="label-wrap">
              <label for="city-to">To</label>
            </div>

            <input
              aria-required="true"
              id="flightTrendsSearchArriveArea"
              class="text city-component"
              placeholder="please choose"
            />
          </div>

          <div class="input-group calendar-group input-single">
            <div class="label-wrap">
              <label for="4-1-3-input" class="form-item-label">Departure</label>
            </div>

            <input
              aria-required="true"
              id="4-1-3-input"
              class="text calendar-input calendar-input-disabled-before depart-input calendar-flag"
              placeholder="please choose"
            />
            <i class="icon-zh icon-zh-calendar calendar"></i>
          </div>
        </form>

        <div class="btn-box">
          <a
            role="button"
            data-tabEnd
            href="javascript:"
            aria-description="search button"
            aria-label="search button"
            class="submit-btn __main-button"
            >Search</a
          >
        </div>
      </div>

      <div id="tab2plane" class="plane-4-inner-plane">
        <form
          class="form-items-wrap form-items-wrap-start"
          role="form"
          aria-description="By flight number form"
        >
          <div class="input-group input-single">
            <div class="label-wrap">
              <label for="4-2-1-input" class="form-item-label"
                >Flight number</label
              >
            </div>

            <input
              aria-required="true"
              id="4-2-1-input"
              class="text"
              placeholder="please choose"
              title="please choose"
            />
          </div>

          <div class="input-group calendar-group input-single">
            <div class="label-wrap">
              <label for="4-2-2-input">Departure</label>
            </div>

            <input
              aria-required="true"
              id="4-2-2-input"
              class="text calendar-input depart-input calendar-input-disabled-before calendar-flag"
              placeholder="please choose"
            />
            <i class="icon-zh icon-zh-calendar calendar"></i>
          </div>
        </form>

        <div class="btn-box">
          <a
            href="javascript:"
            role="button"
            aria-description="search button"
            class="submit-btn __main-button"
          >
            Search
          </a>
        </div>
      </div>
    </div>
  </section>
</div>

<div class="mobile-from-tab-wrap">
  <div class="tab">
    <div
      class="tab-header"
      id="mobileTab1"
      onclick="toggleTab(0)"
      onfocus="toggleTab(0)"
    >
      <div class="tab-header-text">Flight</div>
      <i class="icon icon-zh icon-zh-add"></i>
    </div>
    <div class="tab-body">
      <%- include('./flights') %>
    </div>
  </div>
  <div class="tab">
    <div
      class="tab-header"
      id="mobileTab2"
      onclick="toggleTab(1)"
      onfocus="toggleTab(1)"
    >
      <div class="tab-header-text">Seat selection check-in</div>
      <i class="icon icon-zh icon-zh-add"></i>
    </div>
    <div class="tab-body">
      <div class="book-seat-check">
        <div
          aria-description="seat selection check-in"
          class="form-seat-check tab-target"
        >
          <div class="form-tab clearfix">
            <div class="tab active" role="button">Seat selection check-in</div>
            <div class="tab" role="button">Record Search</div>
          </div>
          <div class="form-wrap">
            <div class="form-items active">
              <form class="form-table">
                <div class="form-seat-flex">
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="“idCard"
                          >Please enter your ID card number</label
                        >
                      </div>
                      <input
                        type="text"
                        id="idCard"
                        class="text"
                        placeholder="Please fill in the input"
                        value=""
                        aria-required="true"
                      />
                    </div>
                  </div>
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="firstName">First Name</label>
                      </div>
                      <input
                        type="text"
                        id="firstName"
                        class="text"
                        placeholder="Please fill in the input"
                        value=""
                        aria-required="true"
                      />
                    </div>
                  </div>
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="lastName">Last name/Surnname</label>
                      </div>
                      <input
                        type="text"
                        id="lastName"
                        class="text"
                        placeholder="Please fill in the input"
                        value=""
                        aria-required="true"
                      />
                    </div>
                  </div>
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="flightNo"
                          >Please input the flight number</label
                        >
                      </div>
                      <input
                        type="text"
                        id="flightNo"
                        class="text"
                        placeholder="Please fill in the input"
                        value=""
                        aria-required="true"
                      />
                    </div>
                  </div>
                  <div class="input-wrap calendar-group">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="single-date" class="title">Departure</label>
                      </div>
                      <input
                        type="text"
                        class="text calendar-input depart-input calendar-flag"
                        id="single-date"
                        placeholder="2024-05-01"
                        aria-required="true"
                        aria-label="calendar，please press enter to choose date"
                      />
                      <i class="icon-zh icon-zh-calendar calendar"></i>
                    </div>
                  </div>
                </div>
                <div class="check-read">
                  <label
                    class="checkbox-group is-select"
                    aria-checked="false"
                    role="checkbox"
                  >
                    <span class="checkbox"
                      ><input type="checkbox" /><i
                        class="icon-zh icon-zh-success-yuan"
                      ></i
                    ></span>
                  </label>
                  <p>
                    Agree to use
                    <a href="javascript:" class="link"
                      >Online Check in Agreement and Notice for Passengers
                      Carrying Dangerous Goods</a
                    >
                  </p>
                </div>
                <ul class="warm-tips">
                  <li class="title">Gentle hint:</li>
                  <li>
                    • The seat selection & check-in function is only for adults
                    and child passengers accompanied by adults; after seat
                    pre-selection, passengers with babies or applying for
                    special services should go to the manual check-in counter of
                    Shenzhen Airlines 2 hours before the flight departure to
                    check in.
                  </li>
                  <li>
                    • Please select seats and check in at official channels of
                    Shenzhen Airlines.
                  </li>
                </ul>
                <div class="btn-box text-right">
                  <a href="javascript:" role="button" class="btn"
                    >Seat selection check-in</a
                  >
                </div>
              </form>
            </div>
            <div class="form-items">
              <form class="form-table">
                <div class="form-seat-flex">
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="“idCard"
                          >Please enter your ID card number</label
                        >
                      </div>
                      <input
                        type="text"
                        id="idCard"
                        class="text"
                        placeholder="Please fill in the input"
                        value=""
                        aria-required="true"
                      />
                    </div>
                  </div>
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="firstName">First Name</label>
                      </div>
                      <input
                        type="text"
                        id="firstName"
                        class="text"
                        placeholder="Please fill in the input"
                        value=""
                        aria-required="true"
                      />
                    </div>
                  </div>
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="lastName">Last name/Surnname</label>
                      </div>
                      <input
                        type="text"
                        id="lastName"
                        class="text"
                        placeholder="Please fill in the input"
                        value=""
                        aria-required="true"
                      />
                    </div>
                  </div>
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="flightNo"
                          >Please input the flight number</label
                        >
                      </div>
                      <input
                        type="text"
                        id="flightNo"
                        class="text"
                        placeholder="Please fill in the input"
                        value=""
                        aria-required="true"
                      />
                    </div>
                  </div>
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="single-date" class="title">Departure</label>
                      </div>
                      <input
                        type="text"
                        class="text calendar-input depart-input calendar-flag"
                        id="single-date"
                        placeholder="2024-05-01"
                        aria-required="true"
                        aria-label="calendar，please press enter to choose date"
                      />
                      <i class="icon-zh icon-zh-calendar calendar"></i>
                    </div>
                  </div>
                </div>
                <div class="check-read">
                  <label
                    class="checkbox-group is-select"
                    aria-checked="false"
                    role="checkbox"
                  >
                    <span class="checkbox"
                      ><input type="checkbox" /><i
                        class="icon-zh icon-zh-success-yuan"
                      ></i
                    ></span>
                  </label>
                  <p>
                    Agree to use
                    <a href="javascript:" class="link"
                      >Online Check in Agreement and Notice for Passengers
                      Carrying Dangerous Goods</a
                    >
                  </p>
                </div>
                <ul class="warm-tips">
                  <li class="title">Gentle hint:</li>
                  <li>
                    • The seat selection & check-in function is only for adults
                    and child passengers accompanied by adults; after seat
                    pre-selection, passengers with babies or applying for
                    special services should go to the manual check-in counter of
                    Shenzhen Airlines 2 hours before the flight departure to
                    check in.
                  </li>
                  <li>
                    • Please select seats and check in at official channels of
                    Shenzhen Airlines.
                  </li>
                </ul>
                <div class="btn-box text-right">
                  <a href="javascript:" role="button" class="btn">query</a>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="tab">
    <div
      class="tab-header"
      id="mobileTab3"
      onclick="toggleTab(2)"
      onfocus="toggleTab(2)"
    >
      <div class="tab-header-text">My booking</div>
      <i class="icon icon-zh icon-zh-add"></i>
    </div>
    <div class="tab-body">
      <form>
        <div class="input-group input-single">
          <div class="label-wrap">
            <label>Type</label>
          </div>

          <input
            onfocus="openDropdown(this)"
            class="text"
            placeholder="please choose"
            value="hello world1"
          />
          <i
            class="icon-zh icon-zh-right-type1 dropdown-icon"
            alt="select icon"
          ></i>
        </div>

        <div class="input-group input-single">
          <div class="label-wrap">
            <label>Order NO./TicketNO.</label>
          </div>

          <input disabled class="text" placeholder="please choose" />
        </div>

        <div class="input-group input-single">
          <div class="label-wrap">
            <label>Frist Name</label>
          </div>

          <input disabled class="text" placeholder="please choose" />
        </div>

        <div class="input-group input-single">
          <div class="label-wrap">
            <label>Last name/Surname</label>
          </div>

          <input disabled class="text" placeholder="please choose" />
        </div>

        <div class="input-group input-single">
          <div class="label-wrap">
            <label>Please input the flight number</label>
          </div>

          <input disabled class="text" placeholder="please choose" />
        </div>

        <div class="input-group input-single">
          <div class="label-wrap">
            <label>Departure</label>
          </div>

          <input
            class="text calendar-input calendar-input-disabled-before depart-input calendar-flag"
            placeholder="please choose"
          />
          <i class="icon-zh icon-zh-calendar calendar"></i>
        </div>

        <div class="form-item form-item-end btn-box">
          <a
            role="button"
            href="javascript:"
            aria-label="search button"
            class="submit-btn __main-button"
            >Search</a
          >
        </div>
      </form>
    </div>
  </div>
  <div class="tab">
    <div
      class="tab-header"
      id="mobileTab4"
      onclick="toggleTab(3)"
      onfocus="toggleTab(3)"
    >
      <div class="tab-header-text">Flight Status</div>
      <i class="icon icon-zh icon-zh-add"></i>
    </div>
    <div class="tab-body">
      <div class="inner-tab-wrap">
        <div
          onfocusin="toggleMobilePlane4InnerTab(0)"
          id="mobilePlane4InnerBtn1"
          onclick="toggleMobilePlane4InnerTab(0)"
          class="inner-tab-item"
        >
          Buy route
        </div>
        <div
          onfocusin="toggleMobilePlane4InnerTab(1)"
          id="mobilePlane4InnerBtn2"
          onclick="toggleMobilePlane4InnerTab(1)"
          class="inner-tab-item"
        >
          By flight number
        </div>
      </div>

      <div id="mobileTab1plane" class="plane-4-inner-plane">
        <form>
          <div class="form-items-wrap">
            <div class="cb-wrap">
              <div class="cb-left">
                <div class="input-group input-single city-origin">
                  <div class="label-wrap">
                    <label for="mobileFrom">From</label>
                  </div>

                  <input
                    id="flightTrendsSearchdepartAear"
                    class="text city-component"
                    placeholder="please choose"
                  />
                </div>

                <div class="input-group input-single city-origin">
                  <div class="label-wrap">
                    <label for="mobileTo">To</label>
                  </div>

                  <input
                    id="flightTrendsSearchArriveArea"
                    class="text city-component"
                    placeholder="please choose"
                  />
                </div>
              </div>

              <span
                onclick="replacementValue('flightTrendsSearchdepartAear', 'flightTrendsSearchArriveArea')"
                class="cursor-pointer icon-repeatedly icon-zh icon-zh-change-flight change"
              >
              </span>
            </div>

            <div class="input-single input-group input-group-short">
              <div class="label-wrap">
                <label for="mobileTo">Departure</label>
              </div>

              <input
                id="mobileTo"
                class="text calendar-input calendar-input-disabled-before depart-input calendar-flag"
                placeholder="please choose"
              />
              <i class="icon-zh icon-zh-calendar calendar"></i>
            </div>
          </div>

          <div class="btn-box">
            <a
              role="button"
              href="javascript:"
              aria-label="search button"
              class="submit-btn __main-button"
              >Search</a
            >
          </div>
        </form>
      </div>
      <div id="mobileTab2plane" class="plane-4-inner-plane">
        <form>
          <div class="form-items-wrap">
            <div class="input-group input-single">
              <div class="label-wrap">
                <label for="">Flight number</label>
              </div>

              <input class="text" placeholder="please choose" />
            </div>
            <div class="input-single input-group">
              <div class="label-wrap">
                <label for="">Departure</label>
              </div>

              <input
                class="text calendar-input calendar-input-disabled-before depart-input calendar-flag"
                placeholder="please choose"
              />
              <i class="icon-zh icon-zh-calendar calendar"></i>
            </div>

            <div class="form-item form-item-end btn-box">
              <a
                role="button"
                href="javascript:"
                aria-label="search button"
                class="submit-btn __main-button"
                >Search</a
              >
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
