/**
 *
 * @type {HTMLDialogElement}
 */
const dialog = document.getElementById('privacy-setting');

const switch1 = document.getElementById('switch1');
const switch2 = document.getElementById('switch2');
const switch3 = document.getElementById('switch3');

const openDialog = () => {
  if (dialog.open) return;

  dialog.show();
};

const closeDialog = () => {
  if (!dialog.open) return;

  dialog.close();
};

/**
 *
 * @param {HTMLElement} dom
 */
const toggleStatus = dom => {
  const isDisable = dom.classList.contains('check-box-disabled');
  if (isDisable) return;

  dom.classList.toggle('check-box-active');
};

/**
 * 切换开关
 * @param {boolean} flag
 */
const changeSwitch = flag => {
  if (flag) {
    switch2.classList.add('check-box-active');
    switch3.classList.add('check-box-active');
  } else {
    switch2.classList.remove('check-box-active');
    switch3.classList.remove('check-box-active');
  }
};

/**
 * 初始化直接打开
 */
// openDialog();
