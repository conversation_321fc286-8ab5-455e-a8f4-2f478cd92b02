.privacy-setting2 {
  width: 100%;
  height: 100vh;
  background: rgba(16, 16, 16, 0.4);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  padding: 0;
  outline: none;
  border: none;
  color: #101010;

  &-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
  }

  &-container {
    width: 25rem;
    height: 14rem;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 0.4rem;
    .screenMobile({
      width: calc(100vw - 3rem);
      height: 14.65rem;
    });

    .privacy-setting2-header {
      position: relative;
      height: 3rem;
      background: #dbd7d4;
      text-align: center;
      line-height: 3rem;
      font-size: 1rem;
      border-top-left-radius: 0.4rem;
      border-top-right-radius: 0.4rem;

      .close {
        cursor: pointer;
        position: absolute;
        right: 2rem;
        top: 50%;
        transform: translateY(-50%);
      }

      .screenMobile({
        font-size: 1rem;
      });
    }

    .privacy-setting2-body {
      margin: 2rem 1rem;

      .screenMobile({
        margin: 1rem 1rem;
      });

      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        .screenMobile({
          flex-direction: column;
        });

        .input-block {
          width: 11rem;
          height: 2.5em;

          .error-tips {
            display: none;
            font-size: 0.7rem;
            color: #ff4d4f;
          }
          .text {
            width: 100%;
            padding: 0.55rem 0.75rem;
            padding-right: 1.2rem;
            border: 0.1rem solid @gray-83;
            outline: none;
            border-radius: 0.25rem;
            font-size: 0.8rem;
          }
          .l-info {
            display: inline-block;
            margin: 0.55rem 0;
            width: 3.8rem;
            padding-left: 0.75rem;
            border-right: 2px solid @gray-83;
          }
          .r-text {
            width: -webkit-calc(100% - 4.1rem);
            width: calc(100% - 4.1rem);
            border: none;
            outline: none;
            padding: 0.55rem 0.75rem;
            padding-right: 1.2rem;
          }
          .text-entry {
            border: 0.1rem solid @gray-83;
            border-radius: 0.25rem;
          }
          .clean-txt {
            display: none;
            position: absolute;
            padding: 0;
            right: 0.7rem;
            top: 0.75rem;
            width: 1.1rem;
            height: 1.3rem;
            color: @gray-dark;
            border: none;
            cursor: pointer;
            .icon-close {
              font-size: 1rem;
            }
          }
          .badge-info {
            position: absolute;
            left: 0;
            top: 2.85rem;
            z-index: 99;
            display: none;
          }

          &.filled {
            .clean-txt {
              display: block;
              display: none\0; //IE9使用自身效果
              &:focus {
                display: block !important;
                display: none\0; //IE9使用自身效果
                outline: none;
                color: @primary-color;
              }
            }
          }

          &.disabled {
            cursor: not-allowed;
            .text,
            .r-text,
            .text-entry,
            label {
              color: #dbd7d4;
            }
            .icon-zh {
              color: #dbd7d4;
            }
            .text {
              cursor: not-allowed;
              &:-ms-input-placeholder {
                color: #dbd7d4 !important;
              }
              &::-moz-placeholder {
                color: #dbd7d4 !important;
              }
              &:-moz-placeholder {
                color: #dbd7d4 !important;
              }
              &::-webkit-input-placeholder {
                color: #dbd7d4 !important;
              }
              &::placeholder {
                color: #dbd7d4 !important;
              }
            }
          }

          &:not(:disabled):not(.disabled):hover {
            .text,
            .text-entry {
              border: 0.1rem solid #4f3d1e;
            }
          }

          &:not(.disabled):not(.error).active {
            .text,
            .text-entry {
              border: 0.1rem solid #4f3d1e;
            }
          }

          &.error {
            .text,
            .text-entry {
              border: 0.1rem solid #ff4d4f;
              color: #ff4d4f;
            }
            &:not(:disabled):not(.disabled):hover {
              .text,
              .text-entry {
                border: 0.1rem solid #ff4d4f !important;
              }
            }
            .error-tips {
              display: block;
              position: absolute;
              margin-top: 5px;
              @media (max-width: @screen-xs-max) {
                position: initial;
              }
            }
          }

          .screenMobile({
            width: 100%;
            margin-bottom: 1rem;
          });
        }

        .text {
          width: 100%;
          height: 100%;
          border: 0.05rem solid #cccccc;
          padding: 0 0.48rem;

          .screenMobile({
            font-size: 0.8rem;
          });
        }

        .code-block {
          display: flex;
          align-items: center;
          .screenMobile({
            width: 100%;
          });
        }

        .code {
          width: 7.8rem;
          height: 2.5rem;
          cursor: pointer;
          margin-right: 1rem;
          font-size: 0.8rem;
        }

        .icon-refresh {
          color: @primary-color;
          &:hover {
            color: #942531;
          }
          cursor: pointer;
          width: 1.3rem;
          // height: 1.3rem;
        }
      }
    }

    .privacy-setting-footer {
      padding: 0 1.5rem 1.5rem;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      .screenMobile({
        padding: 0 1rem 1.5rem;
      });

      .privacy-setting-footer__button {
        width: 5rem;

        .screenMobile({
          width: 100%;
          height: 2.45em;
        });
      }
    }
  }
}
