.check-box {
  width: 2rem;
  height: 1rem;
  border-radius: 1rem;
  background: #554e49;
  position: relative;
  cursor: pointer;

  &-inner {
    width: calc(1rem - 2px);
    height: calc(1rem - 2px);
    border-radius: 1rem;
    background: #ffffff;
    position: absolute;
    left: 1px;
    top: 1px;
    transition: 0.2s;
  }

  &-disabled {
    cursor: not-allowed;
    background: #dbd7d4;

    .check-box-inner {
      left: calc(100% - 1rem);
      margin-left: 1px;
    }
  }

  &-active {
    background: #cc0100;

    .check-box-inner {
      left: calc(100% - 1rem);
      margin-left: 1px;
    }
  }
}

.privacy-setting {
  width: 100%;
  height: 100vh;
  background: rgba(16, 16, 16, 0.4);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  padding: 0;
  outline: none;
  border: none;
  color: #101010;

  &-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
  }

  &-container {
    width: 35.5rem;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 0.4rem;

    .screenPad({
      width : calc(100vw - 60px);
    });

    .screenMobile({
      width : calc(100vw - 1rem);
    });

    .privacy-setting-header {
      position: relative;
      height: 3rem;
      background: #dbd7d4;
      text-align: center;
      line-height: 3rem;
      font-size: 1.4rem;
      border-top-left-radius: 0.4rem;
      border-top-right-radius: 0.4rem;

      .close {
        cursor: pointer;
        position: absolute;
        right: 2rem;
        top: 50%;
        transform: translateY(-50%);
      }

      .screenMobile({
        font-size : 1.2rem;
      });
    }

    .privacy-setting-top {
      margin: 1.5rem;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .screenMobile({
        margin         : 1.5rem 1rem;
        flex-direction : column;
        align-items    : flex-start;
      });

      .tip {
        font-size: 1rem;

        .screenMobile({
          margin-bottom : 0.5rem;
        });
      }

      .buttons {
        display: flex;
        align-items: center;

        .btn {
          padding: 0 1rem;
          margin-right: 1rem;

          &:last-child {
            margin-right: 0;
            background: transparent;
            color: #cc0100;
            border: 1px solid #cc0100;

            &:hover,
            &:active,
            &:focus {
              border: 1px solid #942531;
            }
          }
        }
      }
    }

    .privacy-setting-content {
      .screenMobile({
        height: 23rem;
        overflow-y: scroll;
      });
    }

    .privacy-setting-body {
      margin: 1.5rem;

      .screenMobile({
        margin : 1.5rem 1rem;
      });

      .item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 1rem;

        .check-box {
          width: 2rem;
          margin-right: 1rem;
        }

        .right {
          flex: 1;

          .title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;

            .screenMobile({
              font-size : 0.8rem;
            });
          }

          .content {
            font-size: 0.7rem;
            line-height: 1rem;
            color: #4f3d1e;

            .screenMobile({
              font-size : 0.6rem;
            });
          }
        }
      }
    }

    .privacy-setting-footer {
      padding: 1rem 1.25rem 1rem 1.25rem;
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .screenMobile({
      padding        : 1rem 1.25rem 1rem 1.25rem;
      });

      .privacy-setting-footer__button {
        padding: 0 1rem;
        .screenMobile({
          width : 100%;
        });
      }
    }
  }
}
