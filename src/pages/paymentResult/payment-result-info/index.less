@import '../../../less/variables.less';
@import '../../../less/mediaMixin.less';

.payment-result-info {
  .itinerary-info {
    .notes {
      background: #fdf7e8;
      border: 1px solid #eeb71c;
      padding: 10px 20px;
      margin-bottom: 30px;

      .screenMobile({
        padding: 10px;
        margin-bottom: 20px;
      });

      .notes-title {
        margin-bottom: 10px;

        font-size: 16px;
        font-weight: 500;
        color: @gray-5;

        .screenMobile({
          font-size: 14px;
        });
      }

      .notes-text {
        font-size: 14px;
        color: @gray-5;

        .screenMobile({
          font-size: 12px;
        });
      }
    }

    .infos {
      border: 1px solid #ccc;
      padding: 10px 20px;
      margin-bottom: 30px;

      font-size: 14px;
      color: @gray-5;
      line-height: 1.3;

      .screenMobile({
        padding: 10px;
        margin-bottom: 20px;
      });
    }
  }

  // 行程操作区域样式
  .itinerary-actions {
    display: flex;
    flex-direction: row;
    gap: 20px;
    margin-top: 30px;

    .screenMobile({
      gap: 10px;
      flex-wrap: wrap;
    });

    .email-input-container {
      flex: 1;
      height: 52px;
      border: 1px solid #cccccc; /* 中性色/gray-2 */
      border-radius: 8px;
      padding: 20px;
      display: flex;
      align-items: center;
      background-color: @gray-0; /* 中性色/gray-0 */

      .screenMobile({
        height: 40px;
        padding: 10px;
      });

      .email-input {
        width: 100%;
        border: none;
        outline: none;
        background: transparent;
        font-size: 16px;
        color: @gray-5;

        &::placeholder {
          color: @gray-2;
        }

        .screenMobile({
          font-size: 14px;
        });
      }
    }

    .send-btn,
    .save-print-btn {
      height: 52px;
      border: none;
      border-radius: 8px;
      background-color: @brand-1;
      color: #ffffff; /* 中性色/gray-0 */
      font-size: 20px;
      font-weight: 400;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.3s ease;

      &:hover {
        background-color: darken(@brand-1, 10%);
      }

      .screenMobile({
        height: 40px;
        font-size: 12px;
      });
    }

    .send-btn {
      width: 238px;
      min-width: 238px;

      .screenPad({
        width: 100%;
        min-width: 120px;
        max-width: 120px;
      });

      .screenMobile({
        width: 100%;
        min-width: 106px;
        max-width: 106px;
      });
    }

    .save-print-btn {
      flex: 1;
      min-width: 551px;

      .screenPad({
        width: 100%;
        min-width: 334px;
      });

      .screenMobile({
        width: 100%;
        min-width: 100%;
      });
    }
  }
}
