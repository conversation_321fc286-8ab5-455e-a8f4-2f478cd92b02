<%
  const flightSummaryData1 = {
    tableRefundId: 'table-payment-info-refund-1',
    tableRefundNotes: [
    'When the refund, change and rescheduling rules for multiple flight segments differ, the strictest standard shall be applied.',
    'Refunds must be requested within one year from the start date of the first - leg journey (if the first flight segment of the ticket remains unused, it is calculated from the date of ticket issuance). No refund will be processed after the expiration of this period. For tickets that do not allow refunds, the fuel surcharge and other related fees will not be refunded.',
    ],
    tableChangeId: 'table-payment-info-change-1',
    tableChangeNotes: [
      'When the change, change and rescheduling rules for multiple flight segments differ, the strictest standard shall be applied.',
      'Refunds must be requested within one year from the start date of the first - leg journey (if the first flight segment of the ticket remains unused, it is calculated from the date of ticket issuance). No change will be processed after the expiration of this period. For tickets that do not allow refunds, the fuel surcharge and other related fees will not be refunded.',
    ],
  }

  const flightSummaryData2 = {
    tableRefundId: 'table-payment-info-refund-2',
    tableChangeId: 'table-payment-info-change-2',
    tableRefundNotes: [
    'When the refund, change and rescheduling rules for multiple flight segments differ, the strictest standard shall be applied.',
    'Refunds must be requested within one year from the start date of the first - leg journey (if the first flight segment of the ticket remains unused, it is calculated from the date of ticket issuance). No refund will be processed after the expiration of this period. For tickets that do not allow refunds, the fuel surcharge and other related fees will not be refunded.',
    ],
    tableChangeNotes: [
      'When the change, change and rescheduling rules for multiple flight segments differ, the strictest standard shall be applied.',
      'Refunds must be requested within one year from the start date of the first - leg journey (if the first flight segment of the ticket remains unused, it is calculated from the date of ticket issuance). No change will be processed after the expiration of this period. For tickets that do not allow refunds, the fuel surcharge and other related fees will not be refunded.',
    ],
  }

  const _type = typeof type !== 'undefined' ? type : undefined; // 默认为 ： 支付成功 TICKET_FAILED ：支付成功，出票失败

%>

<div class="payment-result-info">
  <%- include('../../payment/payment-info/flight-summary-section/index', flightSummaryData1 ) %> <%-
  include('../../payment/payment-info/flight-summary-section/index',flightSummaryData2 ) %> <%-
  include('../../payment/payment-info/passenger-info-section/index' ) %> <%-
  include('../../payment/payment-info/additional-services-section/index') %> <%-
  include('../../payment/payment-info/contact-info-section/index') %> <% if(_type !== 'TICKET_FAILED'){ %> <%-
  include('./itinerary-info-section/index') %> <% } %> <% if(_type !== 'TICKET_FAILED'){ %>
  <!-- 行程操作区域 -->
  <h2 class="section-title">Send to Mobile Phone</h2>

  <div class="itinerary-actions">
    <div class="email-input-container">
      <input type="email" class="email-input" placeholder="" />
    </div>
    <button class="send-btn">Send</button>
    <button class="save-print-btn">Save/Print Itinerary Information</button>
  </div>

  <% } %>
</div>
