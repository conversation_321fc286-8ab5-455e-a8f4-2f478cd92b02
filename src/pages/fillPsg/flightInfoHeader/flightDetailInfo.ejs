<%
const isStop = typeof flightStop === 'undefined' ? false : flightStop;
const isTransit = typeof transit === 'undefined' ? false : transit;
%>

<div class="flight-detail">
    <div class="flight-segment">
        <div class="flight-date flex">
            <div class="dep-time"><span class="time" tabindex="0">10:30</span><span class="date" tabindex="0">2025-07-01</span></div>
            <div class="flex">
                <div class="flight-line">
                </div>
                <div class="flight-duration">
                    <span class="duration-time" tabindex="0"> 1h 0min</span>
                    <% if(isStop){ %>
                        <div class="stop-t" tabindex="0">
                            Stopping in Shanghai
                        </div>
                        <div class="stop-t" tabindex="0">
                            Stopping in guangzhou
                        </div>
                    <% } %>

                </div>
                <div class="flight-line">

                </div>

            </div>

            <div class="arrival-time"><span class="date" tabindex="0">2025-07-01</span><span class="time" tabindex="0">10:30</span></div>

        </div>
        <div class="flex">
            <div class="depart">
                <span tabindex="0"> Beijing Daxing Airport  </span>
                <span tabindex="0">T1</span>
            </div>
            <div class="arrival">
                <span tabindex="0">
                      Shanghai Pudong International AirPort
                </span>

                <span tabindex="0">T1</span>
            </div>
        </div>
        <div class="flex">
            <div>
                <img src="../../../images/fillPsg/air.svg" alt="air icon" />
                <span tabindex="0">ZH8034</span>
                <span class="flight-number" tabindex="0">Boeing 738</span>
                <span class="flight-type" tabindex="0">共享</span>
            </div>
            <div class="flex cabin-item">
                <div>
                    <img src="../../../images/fillPsg/meal.svg" alt="meal">
                    <span class="equity-name" tabindex="0">Meal</span>
                </div>
                <span tabindex="0">
                    Business class/J class
                </span>
            </div>
        </div>
        <div>
            <span tabindex="0">Actual Carrier:Shenzhen Airlines</span>
        </div>
    </div>

    <% if(isTransit) { %>
        <div class="transit-line">

            <div class="t-line"></div>
            <div class="transit-s-text" tabindex="0">
                Transfer to Shanghai 1h30m
            </div>
        </div>
        <div class="flight-segment">
            <div class="flight-date flex">
                <div class="dep-time"><span class="time" tabindex="0">10:30</span><span class="date" tabindex="0">2025-07-01</span></div>
                <div class="flex">
                    <div class="flight-line">
                    </div>
                    <div class="flight-duration">
                        <span class="duration-time" tabindex="0"> 1h 0min</span>
                        <% if(isStop){ %>
                            <div class="stop-t" tabindex="0">
                                Stopping in Shanghai
                            </div>
                            <div class="stop-t" tabindex="0">
                                Stopping in guangzhou
                            </div>
                        <% } %>

                    </div>
                    <div class="flight-line">

                    </div>

                </div>

                <div class="arrival-time"><span class="date" tabindex="0">2025-07-01</span><span class="time" tabindex="0">10:30</span></div>

            </div>
            <div class="flex">
                <div class="depart">
                    <span tabindex="0"> Beijing Daxing Airport  </span>
                    <span tabindex="0">T1</span>
                </div>
                <div class="arrival">
                <span tabindex="0">
                      Shanghai Pudong International AirPort
                </span>

                    <span tabindex="0">T1</span>
                </div>
            </div>
            <div class="flex">
                <div>
                    <img src="../../../images/fillPsg/air.svg" alt="air icon" />
                    <span tabindex="0">ZH8034</span>
                    <span tabindex="0" class="flight-number">Boeing 738</span>
                    <span tabindex="0" class="flight-type">共享</span>
                </div>
                <div class="flex cabin-item">
                    <div>
                        <img src="../../../images/fillPsg/meal.svg" alt="meal">
                        <span tabindex="0" class="equity-name">Meal</span>
                    </div>
                    <span tabindex="0">
                    Business class/J class
                </span>
                </div>
            </div>
            <div>
                <span tabindex="0">Actual Carrier:Shenzhen Airlines</span>
            </div>
        </div>
    <% } %>

</div>