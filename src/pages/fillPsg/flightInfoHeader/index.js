$(document).on('click','.flight-info-header #refund-btn',function (event){

    const flightDetail = $(this).closest('.flight-segment').find('.flight-detail');
    const equityInfo = $(this).closest('.flight-segment').find('.equity-info');
    const refundInfo = $(this).closest('.flight-segment').find('.refund-info');

    flightDetail.removeClass('show');
    equityInfo.removeClass('show');
    refundInfo.toggleClass('show');
    $(this).closest('.flight-segment').find('#equity-btn .arrow-up').removeClass('active');
    $(this).closest('.flight-segment').find('#flight-btn .arrow-up').removeClass('active');
    $(this).find('.arrow-up').toggleClass('active');
    event.stopPropagation();
});

$(document).on('click','.flight-info-header #equity-btn',function (event){
    const flightDetail = $(this).closest('.flight-segment').find('.flight-detail');
    const equityInfo = $(this).closest('.flight-segment').find('.equity-info');
    const refundInfo = $(this).closest('.flight-segment').find('.refund-info');

    flightDetail.removeClass('show');
    refundInfo.removeClass('show');
    equityInfo.toggleClass('show');
    $(this).closest('.flight-segment').find('#refund-btn .arrow-up').removeClass('active');
    $(this).closest('.flight-segment').find('#flight-btn .arrow-up').removeClass('active');
    $(this).find('.arrow-up').toggleClass('active');
    event.stopPropagation();
})
//
$(document).on('click','.flight-info-header #flight-btn',function (event){
    const flightDetail = $(this).closest('.flight-segment').find('.flight-detail');
    const equityInfo = $(this).closest('.flight-segment').find('.equity-info');
    const refundInfo = $(this).closest('.flight-segment').find('.refund-info');

    refundInfo.removeClass('show');
    equityInfo.removeClass('show');
    flightDetail.toggleClass('show');

    $(this).closest('.flight-segment').find('#refund-btn .arrow-up').removeClass('active');
    $(this).closest('.flight-segment').find('#equity-btn .arrow-up').removeClass('active');
    $(this).find('.arrow-up').toggleClass('active');
    event.stopPropagation();
})

$(document).on('click','.flight-summary',function (){
    $('.flight-info-header .flight-detail').removeClass('show');
    $('.flight-info-header .equity-info').removeClass('show');
    $('.flight-info-header .refund-info').removeClass('show');
    $('.flight-info-header .arrow-up').removeClass('active');
});
