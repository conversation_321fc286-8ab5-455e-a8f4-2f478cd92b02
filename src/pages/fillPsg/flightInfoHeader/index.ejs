<%
const segments = typeof data !== 'undefined' ? data : [];
%>

<div class="flight-info-header">
    <% for (let i = 0;i < segments.length;i++){ %>

        <div class="flight-segment">
            <div class="flight-summary">
                <div class="trip-type">
                    <span class="trip-badge" tabindex="0">One Way</span>
                    <span class="flight-date" tabindex="0">06-18 Wed</span>
                </div>

                <div class="flight-route-header">
                    <div class="departure">
                        <div class="city-name" tabindex="0"><PERSON></div>
                        <div class="flight-time" tabindex="0">10:30</div>
                    </div>

                    <div class="route-divider-header">
                        <div class="route-line"></div>
                        <i class="icon-zh icon-zh-flight flight-icon"></i>
                        <div class="route-line"></div>
                    </div>

                    <div class="arrival">
                        <div class="city-name" tabindex="0">BeiJing</div>
                        <div class="flight-time" tabindex="0">12:30</div>
                    </div>
                </div>

                <div class="flight-options">
                    <% if(segments[i].refund){ %>
                        <button class="option-btn" id="refund-btn">
                            <span>Refund</span>
                            <i class="icon-zh icon-zh-right-type1 arrow-up"></i>
                        </button>
                    <% } %>
                    <% if(segments[i].equity){ %>
                        <button class="option-btn" id="equity-btn">
                            <span>Equity</span>
                            <i class="icon-zh icon-zh-right-type1 arrow-up"></i>
                        </button>
                    <% } %>
                    <button class="option-btn" id="flight-btn">
                        <span>Flight details</span>
                        <i class="icon-zh icon-zh-right-type1 arrow-up"></i>
                    </button>
                </div>
            </div>

            <%- include('./refundInfo') %>

            <%- include('./equityInfo') %>


            <%- include('./flightDetailInfo',{
                transit:true,
                flightStop:true,
            }) %>

        </div>

    <% } %>
</div>
