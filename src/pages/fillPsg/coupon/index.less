@import '../../../less/mediaMixin.less';


.coupons {
  .tab-title {
    display: flex;
    gap: 20px;

    .title-item {
      padding: 6px 0;

      &.active {
        border-bottom: 3px solid @brand-1;
      }
    }

    .screenPad({
      font-size: 16px;
    });
    .screenMobile({
      font-size: 16px;
    });
  }

  .tab-list {

    .tab-con {
      display: none;
      margin-top: 16px;

      &.active {
        display: block;
      }

      .con-tit {
        margin-bottom: 20px;
        font-size: 20px;
      }

    }

    .promo-tips {
      margin-top: 24px;
      padding: 10px 16px;
      background: @yellow-1;
      font-size: 14px;
      color: @sub-4;
      line-height: 1.2;

      .tips-tit {
        margin-bottom: 12px;
        font-size: 16px;
        color: @gray-101010;
      }

    }

    .promo {
      .form-item {
        width: 80%;
      }
    }

    .psg-info {
      display: flex;
      justify-content: space-between;
      margin-top: 30px;
      font-size: 20px;


      .screenMobile({
        font-size: 14px;
      });


      .psg-tag {
        padding: 2px 10px;
        border-radius: 4px;
        font-size: 14px;
        background: @orange-3;
        color: @gray-1_1;
      }

      .psg-name {
        vertical-align: middle;
      }
    }

    .red-packet {


      .screenPad({
        font-size: 16px;
      });

      .psg-info{
        .screenPad({
          font-size: 14px;
        });

        .screenMobile({
          font-size: 14px;
        });
      }


      .packet-item {
        position: relative;
        font-size: 16px;
        //display: flex;
        //flex-wrap: wrap;
        margin-bottom: 16px;
        gap: 20px;
        background: @gray-1_1;
        border: 1px solid @gray-2;
        padding: 10px;
        border-radius: 4px;

        .packet-i{
          display: flex;
          flex-wrap: wrap;
          .packet-ii{
            width: 50%;

            .screenMobile({
              width: 100%;
            });
          }
        }

        .packet-label {
          margin: 12px 0;
          color: @gray-101010;
        }

        .packet-value {
          font-weight: 500;
        }

        .choose-btn {
          position: absolute;
          right: 10px;
          bottom: 6px;
          padding: 10px 28px;
          color: @gray-0;
          background: @brand-1;
          border-radius: 8px;
          cursor: pointer;
        }
      }

      .psg-packet {
        margin-bottom: 24px;

        .info-row {
          margin: 12px 0;
          display: flex;

          .screenPad({
            font-size: 14px;
          });

          .screenMobile({
            font-size: 14px;
            display: block;
            margin: 0;
          });
          //justify-content: space-between;

          .info-col {
            min-width: 40%;
            max-width: 40%;

            .screenMobile({
              min-width: 100%;
              max-width: 100%;
              margin: 10px 0;
            });
          }

          .info-price {
            min-width: 20%;
            max-width: 20%;
            text-align: right;

            .screenMobile({
              min-width: 100%;
              max-width: 100%;
              text-align: left;
              margin: 10px 0;
            });
          }
        }
      }


    }
  }
}