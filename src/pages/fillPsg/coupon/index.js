


$(document).on('click','.coupons .tab-title .title-item',function (){

    const key = $(this).data('key');
    const couponsDom =  $(this).closest('.coupons');
    const tabTit = couponsDom.find('.title-item');
    const tabCon = couponsDom.find('.tab-con');
    $(tabTit).removeClass('active');
    $(tabCon).removeClass('active');
    $(this).addClass('active');

    const curCon = couponsDom.find(`.tab-con[data-key=${key}]`);
    // const curCon = tabCon.find(`[data-key=${key}]`)
    curCon.addClass('active');

    console.log('tabCon',tabCon,key);
})