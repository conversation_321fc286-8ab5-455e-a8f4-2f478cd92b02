

.fp {
  // 乘客表单组
  .passenger-form-group {
    background: @gray-0;
    border: 1px solid @gray-2;
    border-radius: 8px;
    overflow: hidden;

    // 乘客表单头部
    .passenger-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      //background: @gray-f7;
      //border-bottom: 1px solid @gray-2;

      .passenger-icon-wrapper {
        display: flex;
        align-items: center;
        gap: 12px;

        .passenger-icon {
          font-size: 14px;
          width: 22px;
          height: 22px;
          color: @gray-e1;
          text-align: center;
          line-height: 22px;
          background: @orange-3;
          border-radius: 4px;

          .screenMobile({
            font-size: 20px;
          });
        }

        .passenger-icon-t {
          vertical-align: top;
        }

        .passenger-label {
          font-size: 28px;
          font-weight: 600;
          color: @gray-5;

          .screenMobile({
            font-size: 16px;
          });
        }

        .passenger-choose {
          font-size: 16px;
          color: @gray-3;
          cursor: pointer;
          //font-style: italic;

          .screenMobile({
            font-size: 12px;
          });

          .choose-frequently-btn:hover {
            text-decoration: underline;
          }
        }
      }

      .expand-collapse-btn {
        cursor: pointer;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid @gray-lighter;
        border-radius: 100%;
        background: @gray-0;
        transition: all 0.3s ease;

        &:hover {
          background: @gray-f5;
          border-color: @gray-light;
        }

        .collapse-text {
          font-size: 18px;
          font-weight: bold;
          color: @gray-dark;

          img {
            height: 12px;
            width: 12px;
            display: inline-block;
          }
        }
      }
    }

    // 乘客表单内容
    .passenger-form-content {
      padding: 24px 20px;

      .screenMobile({
        padding: 16px 15px;
      });
    }

    // 表单行
    .form-row {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;

      .screenMobile({
        flex-direction: column;
        gap: 15px;
        margin-bottom: 15px;
      });

      &:last-child {
        margin-bottom: 0;
      }
    }

    // 表单组（半宽）
    .form-item {
      padding-bottom: 10px;
    }

    .form-contact {
      display: flex;
      width: 50%;

      .screenMobile({
        width: 100%;
      });

      .contact-type{
        width: 35%;

      }

      .contact-number{
        width: 65%;
      }
      .label-wrap {
        width: 300%;
      }

      .input-single.input-group.error .text {
        border: none;
      }
    }
  }

  // 表单操作按钮
  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 40px;

    .screenMobile({
      margin-top: 30px;
    });

    .btn-continue {
      background: @primary-color;
      color: @gray-0;
      padding: 16px 48px;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 160px;

      .screenMobile({
        width: 100%;
        padding: 14px 24px;
        font-size: 15px;
      });

      &:hover {
        background: @primary-dark-color;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(204, 1, 0, 0.3);
      }

      &:active {
        transform: translateY(0);
      }

      &:disabled {
        background: @gray-lighter;
        color: @gray-light;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
    }
  }


  .sz-modal-content{
    width: 1000px;

    .screenMobile({
      width: 80%;
      max-height: 100%;
      overflow-y: auto;
    });
  }
}