<div class="passenger-form-content">
    <div class="form-row">

        <%- include('../../../components/sz-input/input',{
            label:'First Name',
            tipText:'123',
            formName:'firstName',
        }) %>

        <%- include('../../../components/sz-input/input',{
            label:'Last Name',
            errorMsg:'请输入',
            required:true,
            formName:'lastName',
        }) %>
    </div>

    <div class="form-row">
        <%- include('../../../components/sz-select/index',{
            label:'Gender',
            tipText:'8384sele',
            required:true,
            formName:'gender',
            options:[
                {
                    label:'Male',
                    value:'male',
                },
                {
                    label:'Female',
                    value:'female',
                },
            ],
        }) %>

        <%- include('../../../components/sz-select/index',{
            label:'Nationality',
            formName:'nationality',
                options:[
                {
                    label:'中国',
                    value:'CN'
                },
                {
                    label:'美国',
                    value:'US',
                },
            ],
        }) %>
    </div>

    <div class="form-row">

        <%- include('../../../components/sz-datepicker/index',{
            label:'Date of birth',
            tipText:'datepicketips',
            required:true,
            formName:'dateBirth',
        }) %>
        <%- include('../../../components/sz-select/index',{
            label:'Document Type',
            className:'doc-type',
            formName:'docType',
            options:[
                {
                    label:'护照',
                    value:'passport',
                },
                {
                    label:'身份证',
                    value:'idCard',
                },
            ],
        }) %>
    </div>

    <div class="form-row">
        <%- include('../../../components/sz-input/input',{
            label:'Document Number',
            errorMsg:'请输入',
            className:'doc-num',
            required: true,
            formName:'docNumber',
            type:"idCard",
        }) %>

        <%- include('../../../components/sz-select/index',{
            label:'issuing country/Region',
            formName:'region',
            options:[
                {
                    label:'北京',
                    value:'bj',
                },
                {
                    label:'上海',
                    value:'sh',
                }
            ],
        }) %>
    </div>

    <div class="form-row">

        <%- include('../../../components/sz-datepicker/index',{
            label:'Expiration Type',
            formName:'expirationType',
        }) %>

        <%- include('../../../components/sz-input/input',{
            label:'Identity Type',
            formName:'identityType',
            errorMsg:'请输入',
        }) %>
    </div>

    <div class="form-row">
        <%- include('../../../components/sz-input/input',{
            label:'Email',
            required:true,
            formName:'email',
            errorMsg:'请输入',
            type:'email',
        }) %>

        <div class="form-contact">


            <%- include('../../../components/sz-select/index',{
                label:'Contact Information',
                formName:'contactType',
                options:[
                    {
                        label:'北京1',
                        value:'bj',
                    },
                    {
                        label:'上海2',
                        value:'sh',
                    }
                ],
                className:'contact-type',
            }) %>

            <%- include('../../../components/sz-input/input',{
                label:'',
                errorMsg:'请输入',
                formName:'contactNumber',
                className:'contact-number',
            }) %>
        </div>
    </div>
    <div class="form-row">
        <%- include('../../../components/sz-select/index',{
            label:'Frequent Flyer Program',
            formName:'frequentFly',
            options:[
                {
                    label:'北京',
                    value:'bj',
                },
                {
                    label:'上海',
                    value:'sh',
                }
            ],
        }) %>


        <%- include('../../../components/sz-input/input',{
            label:'Frequent Flyer Number',
            formName:'frequentFlyNumber',
            errorMsg:'请输入',
        }) %>
    </div>
</div>

