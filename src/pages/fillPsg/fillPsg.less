@import '../../less/variables.less';
@import '../../less/mediaMixin.less';

@import './flightInfoHeader/index';
@import './passengerForms/index';
@import './coupon/index';

.fp {
  .wrap-fill-psg {
    padding: 20px 0;

    .fill-psg-container {
      display: flex;
      gap: 30px;
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 20px;

      .screenPad({
        flex-direction: column;
        gap: 20px;
        padding: 0;
      });

      .screenMobile({
        flex-direction: column;
        gap: 15px;
        padding: 0;
      });
    }
  }

  // 左侧表单区域
  .psg-forms-section {
    flex: 2;
    min-width: 0;

    .psg-forms-content {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .form-item{
      width: 50%;


      .screenMobile({
        width: 100%;
      });

    }

  }


  .passenger-info-title {
    display: flex;
    align-items: end;
    //justify-content: space-between;

    .screenMobile({
      flex-direction: column;
      align-items: flex-start;
      gap: 6px;
      //padding: 16px;
    });

    .passenger-info-t {
      font-size: 24px;
      //font-weight: 600;
      color: @gray-5;
      margin: 0;

      .screenMobile({
        font-size: 18px;
      });
    }

    .required-info {
      margin-left: 6px;
      padding-bottom: 3px;
      font-size: 13px;
      color: @gray-light;
      //font-style: italic;

      .screenMobile({
        font-size: 12px;
      });
    }
  }





  // 折叠状态
  .passenger-form-group.collapsed {
    .passenger-form-content {
      display: none;
    }

    .passenger-header .expand-collapse-btn .collapse-text {
      transform: rotate(-90deg);
    }
  }

  // Coupons按钮部分
  .coupons-section {
    margin: 30px 0 20px;
    text-align: left;

    .btn-coupons {
      background: @primary-color;
      color: @gray-0;
      padding: 12px 32px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      &:hover {
        background: @primary-dark-color;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(204, 1, 0, 0.3);
      }

      .screenMobile({
        width: 40%;
        padding: 12px 24px;
      });
    }
  }

  // 温馨提醒部分
  .warm-reminders {
    background: #FDF7E8;
    border: 1px solid #EEB71C;
    border-radius: 6px;
    padding: 20px;
    margin: 20px 0 30px;

    .screenMobile({
      padding: 16px;
      margin: 16px 0 24px;
    });

    h4 {
      color: #101010;
      font-size: 16px;
      font-weight: 600;
      margin: 0 0 12px 0;

      .screenMobile({
        font-size: 15px;
      });
    }

    p {
      color: #4F3D1E;
      font-size: 14px;
      line-height: 1.4;
      margin: 0 0 8px 0;

      &:last-child {
        margin-bottom: 0;
      }

      .screenMobile({
        font-size: 12px;
        line-height: 1.5;
      });
    }
  }

  // 联系信息部分
  .contact-information-section {
    //width: 100%;
    //min-height: 375px;
    background: @gray-0;


    margin: 30px 0 20px;
    overflow: hidden;

    .contact-header {
      display: flex;
      align-items: center;
      //justify-content: space-between;
      padding: 16px 20px 16px 0;
      //background: @gray-f7;
      //border-bottom: 1px solid @gray-2;

      .screenMobile({
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
        padding: 14px 16px;
      });

      h3 {
        font-size: 18px;
        font-weight: 600;
        color: @gray-5;
        margin: 0;

        .screenMobile({
          font-size: 16px;
        });
      }

      .required-info {
        margin-left: 12px;
        font-size: 13px;
        color: @gray-light;
        //font-style: italic;

        .screenMobile({
          font-size: 12px;
        });
      }
    }

    .contact-form {
      padding: 24px 20px;
      border: 1px solid @gray-2;
      border-radius: 8px;
      box-sizing: border-box;

      .screenMobile({
        padding: 16px 15px;
      });

      .contact-form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 30px;

        .screenMobile({
          flex-direction: column;
          gap: 15px;
          margin-bottom: 15px;
        });

        &:last-child {
          margin-bottom: 0;
        }
      }

      .contact-form-group {
        flex: 1;
        display: flex;
        flex-direction: column;

        &.full-width {
          flex: 2;
        }

        label {
          font-size: 14px;
          color: @gray-dark;
          margin-bottom: 6px;
          font-weight: 500;

          .screenMobile({
            font-size: 13px;
          });
        }

        input,
        select {
          width: 100%;
          padding: 12px 16px;
          border: 1px solid @gray-2;
          border-radius: 6px;
          font-size: 14px;
          color: @gray-5;
          background: @gray-0;
          transition: border-color 0.3s ease, box-shadow 0.3s ease;

          .screenMobile({
            padding: 10px 12px;
            font-size: 13px;
          });

          &:focus {
            outline: none;
            border-color: @primary-color;
            box-shadow: 0 0 0 3px rgba(204, 1, 0, 0.1);
          }

          &::placeholder {
            color: @gray-light;
          }

          &:hover:not(:focus) {
            border-color: @gray-light;
          }
        }

        select {
          cursor: pointer;
          background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
          background-repeat: no-repeat;
          background-position: right 12px center;
          background-size: 12px;
          padding-right: 36px;
          appearance: none;
        }
      }
    }
  }

  // 隐私协议部分
  .privacy-agreement {
    margin: 24px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;

    .checkbox-container {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      cursor: pointer;
      line-height: 1.5;

      .screenMobile({
        gap: 10px;
      });

      input[type='checkbox'] {
        display: none;
      }

      .checkmark {
        width: 18px;
        height: 18px;
        border: 2px solid @gray-light;
        border-radius: 3px;
        background: @gray-0;
        position: relative;
        flex-shrink: 0;
        margin-top: 2px;
        transition: all 0.3s ease;

        .screenMobile({
          width: 16px;
          height: 16px;
        });

        &::after {
          content: '';
          position: absolute;
          left: 5px;
          top: 1px;
          width: 4px;
          height: 8px;
          border: solid @gray-0;
          border-width: 0 2px 2px 0;
          transform: rotate(45deg);
          opacity: 0;
          transition: opacity 0.3s ease;

          .screenMobile({
            left: 4px;
            top: 0px;
            width: 3px;
            height: 7px;
          });
        }
      }

      input[type='checkbox']:checked + .checkmark {
        background: @primary-color;
        border-color: @primary-color;

        &::after {
          opacity: 1;
        }
      }

      .agreement-text {
        font-size: 14px;
        color: @gray-dark;

        .screenMobile({
          font-size: 13px;
        });

        .privacy-link {
          color: @primary-color;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }

  // Next按钮部分
  .next-actions {
    display: flex;
    justify-content: center;
    //margin: 30px 0 40px;

    .screenMobile({
      margin: 24px 0 30px;
    });

    .btn-next {
      background: @gray-1;
      color: @sub-2;
      //padding: 16px 56px;
      border: none;
      border-radius: 8px;
      font-size: 20px;
      //font-weight: 600;
      cursor: not-allowed;
      transition: all 0.3s ease;
      min-width: 140px;
      height: 52px;

      .screenMobile({
        width: 100%;
        min-width: 120px;
        height: 40px;
        font-size: 14px;
      });

      &:not(:disabled) {
        background: @primary-color;
        color: @gray-0;
        cursor: pointer;

        &:hover {
          background: @primary-dark-color;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(204, 1, 0, 0.3);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }

  // 动画过渡
  .passenger-form-content {
    transition: all 0.3s ease;
  }

  .expand-collapse-btn .collapse-text {
    transition: transform 0.3s ease;
  }


  .require {
    color: #CC0100;
  }


  .input-single .label-wrap{
    overflow: visible;
    .bubble-info{

      width: 200px;
      left: 0;
      margin-left: -30px;
      .screenMobile({
        width: 300px;
        white-space: wrap;
        margin-left: 0;
      });
      .screenMobile({
        width: 300px;
        white-space: wrap;
        margin-left: 0;
      });

    }
  }




  // 右侧航班详情区域
  .price-summary-card{
    box-shadow: none;

    .screenPad({
      display: none;
    });
    .screenMobile({
      display: none;
    });
  }


  .total-price-con{
    position: relative;
    .total-price-title{
      justify-content: space-between;
      padding: 20px;
      border: 1px solid @gray-2;
      border-top: none;
      font-size: 24px;
      color: @gray-3;
      display: none;

      .screenMobile({
        display: flex;
      });
      .screenPad({
        display: flex;
      });

      .title-price{
        font-size: 28px;
        color: @brand-1;
        .price-unit{
          font-size: 16px;
          color: @sub-4;
          vertical-align: middle;
        }
      }
      .price-btn{
        margin-left: 30px;
        font-size: 20px;
        cursor: pointer;
        color: @orange-3;

        .arrow-up{
          display: inline-block;
          transform: rotate(90deg);
          margin-bottom: 4px;
          vertical-align: middle;

        }

        &.active .arrow-up{
          transform: rotate(0deg);
        }
      }
    }

    .price-summary-card{
      position: absolute;
      left: 0;
      top: 100%;
      width: 100%;
      display: block;
      z-index: 10;
      background: @gray-0;
    }
  }

}
