/**
 * 乘客表单模块 JavaScript
 * 处理表单交互、验证和数据提交
 */


(function () {

  // 初始化页面
  function initFillPsgPage() {
    initCollapseToggle();
    // initFormValidation()
  }

  // 初始化折叠/展开功能
  function initCollapseToggle() {
    const toggleButtons = document.querySelectorAll('.expand-collapse-btn')

    toggleButtons.forEach((button) => {
      button.addEventListener('click', function () {
        const formGroup = this.closest('.passenger-form-group')
        if (formGroup.classList.contains('collapsed')) {
          formGroup.classList.remove('collapsed')
        } else {
          formGroup.classList.add('collapsed')
        }
      })
    })

    $(document).on('click','.total-price-title #price-btn',function (){
      const priceDom = $('.price-summary-card');
      $(this).toggleClass('active');
      if(!$(this).hasClass('active')){
        $('.fill-psg-container').append(priceDom);
      }else{
        $(this).closest('.total-price-con').append(priceDom);
      }
    });

    $(document).on('click','#btn-next',function (){

      //判断是否都通过了验证  判断form-item上面是否还存在errorclass。没有就是通过了验证
      if(validateAllForms()){
        const values = getFormValues($('#psg-adult'));
        const valuesChd = getFormValues($('#psg-chd'));
        const valuesInf = getFormValues($('#psg-inf'));

        console.log('当前表单的数据',values,valuesInf,valuesChd);
      }else{
        console.log('还有校验未过');
      }

      console.log('aaaa',allInputs);
    })

    $(document).on('change','.doc-type input',function (){
      console.log('tttt',this.dataset.value);
      const curValue = this.dataset.value;
      // const curValue = $(this).find('input').data('value');

      //给docNum重新的校验规则类型
      const docNum = $(this).closest('.passenger-form-content').find('.doc-num input');
      docNum.data('type', curValue);

    })
  }

  // 获取乘客类型
  function getPassengerType(fieldId) {
    if (fieldId.includes('adult')) return 'adult'
    if (fieldId.includes('child')) return 'child'
    if (fieldId.includes('infant')) return 'infant'
    return ''
  }

  // 获取年龄限制提示信息
  function getAgeLimitMessage(passengerType) {
    switch (passengerType) {
      case 'adult':
        return '成人乘客年龄应满18岁'
      case 'child':
        return '儿童乘客年龄应在2-11岁之间'
      case 'infant':
        return '婴儿乘客年龄应在0-2岁之间'
      default:
        return '请输入有效的出生日期'
    }
  }


  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initFillPsgPage)
  } else {
    initFillPsgPage();
    $('.calendar-input').dateRangePicker();
  }
})()