@import '../../../less/variables.less';
@import '../../../less/mediaMixin.less';

.seven-day-calendar {
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    font-size: 28px;
    font-weight: 500;
    color: @gray-5;

    .screenMobile({
      font-size: 20px;
      flex-direction: column;
      align-items: flex-start;
    });

    &-text {
      display: flex;
      align-items: center;

      font-size: 28px;
      font-weight: 400;

      .screenMobile({
        margin-top: 12px;
        font-size: 16px;
      });
    }

    &-i {
      margin-right: 6px;

      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 9999px;

      box-sizing: border-box;
      border: 2px solid @brand-1;
    }
  }

  .sorry {
    padding: 20px;
    margin: 20px 0;
    background: #fdf7e8;
    border: 1px solid #eeb71c;

    font-size: 14px;
    color: @gray-5;

    .screenMobile({
      margin: 10px 0;
      padding: 10px;

      font-size: 12px;

    });
  }

  .error {
    padding: 20px;
    margin: 20px 0;
    background: #fdf7e8;
    border: 1px solid #eeb71c;

    .screenMobile({
      margin: 10px 0;
      padding: 10px;
    });

    .error-top {
      display: flex;
      align-items: center;

      .clock {
        width: 30px;
        height: 30px;

        .screenMobile({
          width: 20px;
          height: 20px;
        });
      }
    }

    .error-title {
      font-size: 24px;
      color: @orange-3;
      margin-left: 8px;

      .screenMobile({
        font-size: 20px;
      });
    }

    .error-message {
      margin-top: 10px;

      font-size: 14px;
      color: @gray-5;

      .screenMobile({
        font-size: 12px;
      });
    }
  }

  &-body {
    margin-top: 20px;
    margin-bottom: 20px;

    .screenMobile({
      margin-top: 10px;
      margin-bottom: 10px;
    });

    .schedule-container {
      display: flex;
      flex-direction: column;
      border: 1px solid #ccc;
      background-color: #fff;
      border-radius: 8px;
      overflow: hidden;

      .header-row,
      .data-row {
        display: flex;
        // border-bottom: 1px solid #eee;

        &:last-child {
          border-bottom: none;
        }
      }

      .header-row {
        .corner-cell {
          font-size: 16px;
          font-weight: 500;
          color: @gray-4;

          .screenPad({
            font-size: 12px;
          });

          .screenMobile({
            font-size: 10px;
            font-weight: 400;
          });
        }
      }

      .corner-cell,
      .outbound-cell {
        flex-basis: 150px; // Fixed width for outbound column
        flex-shrink: 0;
        padding: 10px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        // border-right: 1px solid #eee;
        background-color: @sub-1;

        .screenPad({
          flex-basis: 90px;
        });

        .screenMobile({
          flex-basis: 80px;
        });
      }

      .outbound-cell {
        font-size: 16px;
        color: #554e49;

        .screenMobile({
          font-size: 10px;
        });
      }

      .inbound-headers {
        background-color: @sub-1;
      }

      .inbound-headers,
      .inbound-data {
        display: flex;
        flex-grow: 1; // Allows inbound columns to take remaining space
      }

      .inbound-date,
      .inbound-value {
        flex: 1; // Distributes space evenly among inbound cells
        padding: 10px;
        text-align: center;
        // border-right: 1px solid #eee;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        white-space: nowrap;

        font-size: 16px;
        color: #554e49;

        &:last-child {
          border-right: none;
        }

        .screenMobile({
          padding: 6px 2px;

          font-size: 10px;
        });
      }

      .inbound-value {
        color: @gray-5;
      }

      .red-dot {
        margin-top: 10px;

        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 9999px;

        box-sizing: border-box;
        // border: 2px solid @brand-1;
        background: @brand-1;

        .screenMobile({
          width: 6px;
          height: 6px;
        });
      }
    }
  }

  &-footer {
    font-size: 20px;
    color: @gray-5;

    .screenMobile({
      font-size: 12px;
    });
  }

  .corner-cell {
    position: relative;

    .diagonal-line {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(-160deg, transparent 49.5%, #ccc 50%, transparent 50.5%);
      pointer-events: none; /* 避免线条遮挡事件 */

      .screenPad({
        background: linear-gradient(-148deg, transparent 49.5%, #ccc 50%, transparent 50.5%);
      });

      .screenMobile({
        background: linear-gradient(-156deg, transparent 49.5%, #ccc 50%, transparent 50.5%);
      });
    }

    .inbound,
    .outbound {
      position: absolute;
    }

    .inbound {
      top: 6%;
      right: 2%;

      .screenMobile({
        top: 4%;
      });
    }

    .outbound {
      bottom: 6%;
      left: 2%;

      .screenMobile({
        bottom: 4%;
      });
    }
  }
}
