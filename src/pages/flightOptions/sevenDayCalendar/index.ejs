<div class="seven-day-calendar" role="main" aria-label="Seven day flight calendar">
  <!-- error -->
  <div class="seven-day-calendar-header" role="banner">
    <span>Select your flight</span>
  </div>

  <div class="sorry">Sorry, XXXXXXXXXXXXXXXXXX</div>

  <div class="error" role="alert" aria-live="polite">
    <div class="error-top">
      <img class="clock" src="../../images/flightOptions/clock.svg" alt="Clock icon" />
      <div class="error-title">Error</div>
    </div>
    <div class="error-message">
      Sorry, there are no applicable flight prices for the day of the second itinerary you searched
    </div>
  </div>

  <div class="seven-day-calendar-header" role="banner">
    <span>Select another date</span>
    <span class="seven-day-calendar-header-text">
      <i class="seven-day-calendar-header-i" role="img" aria-label="Lowest price indicator" aria-hidden="true"></i>
      Lowest price
    </span>
  </div>

  <div class="seven-day-calendar-body">
    <div class="schedule-container" role="table" aria-label="Flight price calendar">
      <div class="header-row" role="row">
        <div class="corner-cell" role="columnheader">
          <div class="diagonal-line" aria-hidden="true"></div>
          <div class="inbound">INBOUND</div>
          <div class="outbound">OUTBOUND</div>
        </div>
        <div class="inbound-headers" role="rowgroup">
          <div class="inbound-date" role="columnheader" aria-label="June 22, Sunday">
            06-22
            <br />
            <span>Sun</span>
          </div>
          <div class="inbound-date" role="columnheader" aria-label="June 23, Monday">
            06-23
            <br />
            <span>Mon</span>
          </div>
          <div class="inbound-date" role="columnheader" aria-label="June 24, Tuesday">
            06-24
            <br />
            <span>Tue</span>
          </div>
          <div class="inbound-date" role="columnheader" aria-label="June 25, Wednesday">
            06-25
            <br />
            <span>Wed</span>
          </div>
          <div class="inbound-date" role="columnheader" aria-label="June 26, Thursday">
            06-26
            <br />
            <span>Thu</span>
          </div>
          <div class="inbound-date" role="columnheader" aria-label="June 27, Friday">
            06-27
            <br />
            <span>Fri</span>
          </div>
          <div class="inbound-date" role="columnheader" aria-label="June 28, Saturday">
            06-28
            <br />
            <span>Sat</span>
          </div>
        </div>
      </div>
      <% data.sevenDayCalendarData.forEach(row => { %>
      <div class="data-row" role="row">
        <div class="outbound-cell" role="rowheader">
          <div class="outbound-date" aria-label="<%= row.outbound %>, <%= row.outboundDay %>">
            <%= row.outbound %>
            <br />
            <span><%= row.outboundDay %></span>
          </div>
        </div>
        <div class="inbound-data" role="rowgroup">
          <div class="inbound-value" role="cell" aria-label="Price <%= row.inbound['06-22'] %>">
            <%= row.inbound["06-22"] %>
          </div>
          <div class="inbound-value" role="cell" aria-label="Price <%= row.inbound['06-23'] %>">
            <%= row.inbound["06-23"] %>
          </div>
          <div class="inbound-value" role="cell" aria-label="Price <%= row.inbound['06-24'] %>">
            <%= row.inbound["06-24"] %>
          </div>
          <div class="inbound-value" role="cell" aria-label="Price <%= row.inbound['06-25'] %>">
            <%= row.inbound["06-25"] %>
          </div>
          <div
            class="inbound-value"
            role="cell"
            aria-label="Price <%= row.inbound['06-26'] %><% if (row.inbound['06-26'] !== '-') { %>, lowest price<% } %>">
            <%= row.inbound["06-26"] %> <% if (row.inbound["06-26"] !== '-') { %>
            <span class="red-dot" aria-hidden="true"></span>
            <% } %>
          </div>
          <div
            class="inbound-value"
            role="cell"
            aria-label="Price <%= row.inbound['06-27'] %><% if (row.inbound['06-27'] !== '-') { %>, lowest price<% } %>">
            <%= row.inbound["06-27"] %> <% if (row.inbound["06-27"] !== '-') { %>
            <span class="red-dot" aria-hidden="true"></span>
            <% } %>
          </div>
          <div
            class="inbound-value"
            role="cell"
            aria-label="Price <%= row.inbound['06-28'] %><% if (row.inbound['06-28'] !== '-') { %>, lowest price<% } %>">
            <%= row.inbound["06-28"] %> <% if (row.inbound["06-28"] !== '-') { %>
            <span class="red-dot" aria-hidden="true"></span>
            <% } %>
          </div>
        </div>
      </div>
      <% }) %>
    </div>
  </div>

  <p class="seven-day-calendar-footer" role="contentinfo" aria-label="Currency: CNY">币种：CNY</p>
</div>
