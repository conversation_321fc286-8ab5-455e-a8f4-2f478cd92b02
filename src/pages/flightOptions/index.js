tippy('[data-tippy-content]');

// tippy('#myButton', {
//   content: 'My tooltip!',
// });

const modal = new SZModal({
  // title: 'Re-query Flight',
  // okText: 'Search',
  contentDom: $('#modal-content-flight'),
  onOk: () => {
    console.log('ok');
  },
});
// modal.open()

const modalFlightDetails = new SZModal({
  modalDom: $('#flight-details'),
  // title: 'Transfer for 1 times',
  // okText: 'Back',
  contentDom: $('#flight-details-content'),
  cancelText: false,
  // onOk: () => {
  //   console.log('ok')
  // },
});
// modalFlightDetails.open()
