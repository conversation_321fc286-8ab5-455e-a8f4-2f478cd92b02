<div class="price-component" role="article" aria-label="Flight price option">
  <div class="price-tag" aria-label="2 seats remaining">2 Remaining</div>
  <div class="price-header">
    <div class="price-name" aria-label="Business Travel Economy Class">Business Travel Economy Class</div>
  </div>
  <div class="price-content">
    <div class="price-display" aria-label="Total price CNY 7,999">
      <div class="price-currency" aria-hidden="true">CNY</div>
      <div class="price-amount" aria-hidden="true">7,999</div>
    </div>

    <div class="price-details" role="group" aria-label="Price breakdown">
      <div class="detail-row">
        <div class="detail-label">
          <span>Taxes and Fees</span>
        </div>
        <div class="detail-value" aria-label="Taxes and fees CNY 7,099">CNY 7,099</div>
      </div>
      <div class="detail-row">
        <div class="detail-label">
          <span>Fare</span>

          <button
            role="button"
            tabindex="0"
            class="tips"
            aria-label="View fare breakdown details"
            data-tippy-content="<div class='price-component-tippy'>
              <div class='fare'>
                <div class='fare-item'>
                  <span>Taxes and Fees</span>
                  <span>CNY 8</span>
                </div>
                <div class='fare-item'>
                  <span>Fare</span>
                  <span>CNY 18</span>
                </div>
                <div class='fare-item'>
                  <span>Surcharge</span>
                  <span>CNY 34</span>
                </div>
                <div class='fare-item'>
                  <span>Service fee</span>
                  <span>CNY 53</span>
                </div>
                <div class='fare-item'>
                  <span>Taxes and Fees</span>
                  <span>CNY 33</span>
                </div>
                <div class='fare-item'>
                  <span>Taxes and Fees</span>
                  <span>CNY 270</span>
                </div>
              </div>
            </div>"
            data-tippy-allowHTML="true">
            <span class="icon-zh icon-zh-ask" aria-hidden="true"></span>
          </button>
        </div>
        <div class="detail-value" aria-label="Fare CNY 900">CNY 900</div>
      </div>

      <div class="detail-divider" role="separator" aria-hidden="true"></div>

      <div class="detail-features" role="group" aria-label="Flight features">
        <div class="feature-item">
          <div class="feature-icon cabin-icon" role="img" aria-label="Cabin class icon"></div>
          <div class="feature-name">Cabin Class</div>
          <button
            class="feature-value feature-value-hover"
            aria-label="Cabin class U+A, click for details"
            data-tippy-content="<div class='price-component-tippy'>
            <div class='cabin-class'>
              <div class='flight-info-table' role='table' aria-label='Flight details information'>
                <div class='table-column' role='columngroup'>
                  <div class='table-header' role='columnheader'>
                    <span>Flight number</span>
                  </div>
                  <div class='table-body' role='rowgroup'>
                    <div class='table-cell' role='cell'>
                      <span>ZH307</span>
                    </div>
                    <div class='table-cell' role='cell'>
                      <span>ZH8034</span>
                    </div>
                  </div>
                </div>

                <div class='table-column' role='columngroup'>
                  <div class='table-header' role='columnheader'>
                    <span>Flight Segment</span>
                  </div>
                  <div class='table-body' role='rowgroup'>
                    <div class='table-cell' role='cell'>
                      <span>Beijing-Shenzhen</span>
                    </div>
                    <div class='table-cell' role='cell'>
                      <span>Shenzhen-Beijing</span>
                    </div>
                  </div>
                </div>

                <div class='table-column' role='columngroup'>
                  <div class='table-header' role='columnheader'>
                    <span>Cabin Class and Brand</span>
                  </div>
                  <div class='table-body' role='rowgroup'>
                    <div class='table-cell' role='cell'>
                      <span>U(Premium Economy)</span>
                    </div>
                    <div class='table-cell' role='cell'>
                      <span>H(Premium Economy)</span>
                    </div>
                  </div>
                </div>
              </div>
          </div>"
            data-tippy-allowHTML="true">
            U+A
          </button>
        </div>

        <div class="feature-item">
          <div class="feature-icon baggage-icon" role="img" aria-label="Baggage icon"></div>
          <div class="feature-name">
            Baggage
            <button
              role="button"
              tabindex="0"
              class="tips"
              aria-label="View fare breakdown details"
              data-tippy-content="tips tips tips tips tips"
              data-tippy-allowHTML="true">
              <span class="icon-zh icon-zh-ask" aria-hidden="true"></span>
            </button>
          </div>
          <a href="#" class="feature-value feature-value-link" aria-label="Baggage allowance 1 piece">1 Pieces</a>
        </div>

        <div class="feature-item">
          <div class="feature-icon refund-icon" role="img" aria-label="Refund icon"></div>
          <div class="feature-name">Refund</div>
          <button
            class="feature-value feature-value-hover"
            role="button"
            tabindex="0"
            aria-label="Refund fee 300 plus, click for details"
            data-tippy-content="<div class='price-component-tippy'>
            <div class='refund'>
              <div class='refund-rules-container'>
                <div class='refund-rules-table'>
                  <div class='rules-table'>
                    <div class='rules-conditions'>
                      <div class='table-header'>
                        <span>Change of Flight/Cabin Class</span>
                      </div>
              
                      <div class='condition-row'>
                        <div class='condition-time'>
                          <span>After Takeoff</span>
                        </div>
                        <div class='condition-details'>
                          <div class='condition-cell'>
                            <span>All Unused</span>
                          </div>
                          <div class='condition-cell'>
                            <span>Partially Used</span>
                          </div>
                        </div>
                      </div>
              
                      <div class='condition-row'>
                        <div class='condition-time'>
                          <span>Before Takeoff</span>
                        </div>
                        <div class='condition-details'>
                          <div class='condition-cell'>
                            <span>All Unused</span>
                          </div>
                          <div class='condition-cell'>
                            <span>Partially Used</span>
                          </div>
                        </div>
                      </div>
                    </div>
              
                    <div class='refund-amounts'>
                      <div class='table-header'>
                        <span>Refund</span>
                      </div>
              
                      <div class='amount-column'>
                        <div class='amount-cell'>
                          <span>CNY660.0</span>
                        </div>
                        <div class='amount-cell'>
                          <span>CNY660.0</span>
                        </div>
                        <div class='amount-cell'>
                          <span>CNY660.0</span>
                        </div>
                        <div class='amount-cell'>
                          <span>CNY660.0</span>
                        </div>
                      </div>
                    </div>
                  </div>
              
                  <div class='rules-description'>
                    <p>
                      When the refund, change and rescheduling rules for multiple flight segments differ, the
                      strictest standard shall be applied.
                    </p>
              
                    <p>
                      Refunds must be requested within one year from the start date of the first - leg journey (if
                      the first flight segment of the ticket remains unused, it is calculated from the date of
                      ticket issuance). No refund will be processed after the expiration of this period. For
                      tickets that do not allow refunds, the fuel surcharge and other related fees will not be
                      refunded.
                    </p>
                  </div>
                </div>
              </div>
              </div>
          </div>"
            data-tippy-allowHTML="true">
            300+
          </button>
        </div>

        <div class="feature-item">
          <div class="feature-icon change-icon" role="img" aria-label="Change icon"></div>
          <div class="feature-name">Change</div>
          <button
            class="feature-value feature-value-hover"
            role="button"
            tabindex="0"
            aria-label="Change fee 300 plus, click for details"
            data-tippy-content="<div class='price-component-tippy'>
            <div class='change'>
              <div class='change-rules-container'>
                <div class='change-rules-table'>
                  <div class='rules-table'>
                    <div class='rules-conditions'>
                      <div class='table-header'>
                        <span>Change of Flight/Cabin Class</span>
                      </div>
              
                      <div class='condition-row'>
                        <div class='condition-time'>
                          <span>After Takeoff</span>
                        </div>
                        <div class='condition-details'>
                          <div class='condition-cell'>
                            <span>All Unused</span>
                          </div>
                          <div class='condition-cell'>
                            <span>Partially Used</span>
                          </div>
                        </div>
                      </div>
              
                      <div class='condition-row'>
                        <div class='condition-time'>
                          <span>Before Takeoff</span>
                        </div>
                        <div class='condition-details'>
                          <div class='condition-cell'>
                            <span>All Unused</span>
                          </div>
                          <div class='condition-cell'>
                            <span>Partially Used</span>
                          </div>
                        </div>
                      </div>
                    </div>
              
                    <div class='change-amounts'>
                      <div class='table-header'>
                        <span>Change</span>
                      </div>
              
                      <div class='amount-column'>
                        <div class='amount-cell'>
                          <span>CNY660.0</span>
                        </div>
                        <div class='amount-cell'>
                          <span>CNY660.0</span>
                        </div>
                        <div class='amount-cell'>
                          <span>CNY660.0</span>
                        </div>
                        <div class='amount-cell'>
                          <span>CNY660.0</span>
                        </div>
                      </div>
                    </div>
                  </div>
              
                  <div class='rules-description'>
                    <p>
                      When the refund, change and rescheduling rules for multiple flight segments differ, the strictest standard shall be applied. 
                    </p>
              
                    <p>
                      For changes within the same cabin class, if there is a price difference between the fares, the fare difference must be made up. When both a rescheduling fee and an upgrade fee occur, they will be charged simultaneously.
                    </p>
                  </div>
                </div>
              </div>
              </div>
          </div>"
            data-tippy-allowHTML="true">
            300+
          </button>
        </div>

        <div class="feature-item">
          <div class="feature-icon mileage-icon" role="img" aria-label="Mileage icon"></div>
          <div class="feature-name">Base Mileage</div>
          <div class="feature-value" aria-label="Base mileage plus 1142 points">+1142</div>
        </div>

        <div class="feature-item">
          <div class="feature-icon bonus-icon" role="img" aria-label="Bonus icon"></div>
          <div class="feature-name">
            Bonus Mileage
            <button
              role="button"
              tabindex="0"
              class="tips"
              aria-label="View fare breakdown details"
              data-tippy-content="tips tips tips tips tips"
              data-tippy-allowHTML="true">
              <span class="icon-zh icon-zh-ask" aria-hidden="true"></span>
            </button>
          </div>
          <div class="feature-value" aria-label="Bonus mileage plus 1142 points">+1142</div>
        </div>
      </div>
    </div>
  </div>
  <button class="price-footer" tabindex="0" aria-label="Book this flight for CNY 7,999">
    <div class="book-button">
      <div class="book-icon" role="img" aria-label="Book icon" aria-hidden="true"></div>
      <span>BOOK</span>
    </div>
  </button>
</div>
