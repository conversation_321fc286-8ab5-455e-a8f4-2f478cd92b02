<%
  const _routeType = typeof routeType !== 'undefined' ? routeType: undefined; // 1:单程 2:往返 3:多段
%>

<div
  class="flight-and-brand <%= _routeType ===3 && 'multi' %>"
  id="flight-and-brand"
  role="main"
  aria-label="Flight and brand options">
  <div class="notes">
    <div class="notes-title">Notes</div>
    <div class="notes-text">这里是提示样式，文案自行补充</div>
  </div>

  <div class="flight-and-brand-sift" role="toolbar" aria-label="Sort options">
    <button class="flight-and-brand-sift-item __text-button" aria-label="Sort by time">
      <span>Time</span>
      <img class="flight-and-brand-sift-item-img" src="../../images/flightOptions/sort.png" alt="Sort icon" />
    </button>
    <button class="flight-and-brand-sift-item __text-button" aria-label="Sort by price">
      <span>Price</span>
      <img class="flight-and-brand-sift-item-img" src="../../images/flightOptions/sort.png" alt="Sort icon" />
    </button>
  </div>

  <div class="flight-and-brand-list" role="list" aria-label="Flight options">
    <% [...new Array(3)].forEach((_, index) => { %>
    <div class="flight-and-brand-item" role="listitem">
      <div class="flight-card">
        <!-- 航班信息卡片 -->
        <%- include('./flight-info', { isTransfer: index === 0, routeType: _routeType, isStop: index ===2 }) %>

        <!-- 多段舱等 -->
        <% if( _routeType === 3 ){ %>
        <div class="cabin-options-multi" role="group" aria-label="Cabin options">
          <button
            class="cabin-option-multi <%= index === 0 && 'economy' %> <%= index === 1 && 'premium-economy' %> <%= index === 2 && 'business' %>"
            aria-label="Select cabin option, price CNY 1000">
            <div class="cabin-header">
              <div class="cabin-icon-1" role="img" aria-label=""></div>
              <div class="cabin-name">Economy Class</div>
            </div>

            <div class="unit">CNY</div>
            <div class="amount">1000</div>
            <img class="arrow" src="../../images/flightOptions/arrow-right.svg" alt="Select" />
          </button>
        </div>
        <% } %>

        <!-- 非多段舱等 -->
        <% if( _routeType !== 3 ){ %>
        <div class="cabin-options" role="group" aria-label="Cabin class options">
          <button class="cabin-option economy" data-cabin-type="economy" aria-label="Economy Class, CNY 13,999 upwards">
            <div class="cabin-header">
              <div class="cabin-icon-1" role="img" aria-label="Economy class icon"></div>
              <div class="cabin-name">Economy Class</div>
            </div>
            <div class="cabin-price">
              <div class="price-currency">CNY</div>
              <div class="price-amount">13,999</div>
              <div class="price-note">upwards</div>
            </div>
          </button>

          <button
            class="cabin-option premium-economy"
            data-cabin-type="premium-economy"
            aria-label="Premium Economy Class, CNY 13,999 upwards">
            <div class="cabin-header">
              <div class="cabin-icon-1" role="img" aria-label="Premium economy class icon"></div>
              <div class="cabin-name">Premium Economy</div>
            </div>
            <div class="cabin-price">
              <div class="price-currency">CNY</div>
              <div class="price-amount">13,999</div>
              <div class="price-note">upwards</div>
            </div>
          </button>

          <button
            class="cabin-option business"
            data-cabin-type="business"
            aria-label="Business Class, CNY 13,999 upwards">
            <div class="cabin-header">
              <div class="cabin-icon-1" role="img" aria-label="Business class icon"></div>
              <div class="cabin-name">Business Class</div>
            </div>
            <div class="cabin-price">
              <div class="price-currency">CNY</div>
              <div class="price-amount">13,999</div>
              <div class="price-note">upwards</div>
            </div>
          </button>
        </div>
        <% } %>
      </div>

      <!-- 品牌展开部分 -->
      <div class="brand-expanded" data-brand-section role="region" aria-label="Brand options">
        <% if( _routeType === 3 ){ %>

        <!-- 多段航班品牌展开内容 -->
        <%- include('./price-component-multi') %>

        <% } else { %>
        <!-- 非多段航班品牌展开内容 -->

        <!-- 经济舱区域 -->
        <div
          class="brand-options economy-section"
          data-cabin-section="economy"
          role="group"
          aria-label="Economy class price options">
          <div class="price-options-grid">
            <%- include('./price-component') %> <%- include('./price-component') %> <%- include('./price-component') %>
          </div>
        </div>

        <!-- 超级经济舱区域 -->
        <div
          class="brand-options premium-economy-section"
          data-cabin-section="premium-economy"
          role="group"
          aria-label="Premium economy class price options">
          <div class="price-options-grid">
            <%- include('./price-component') %> <%- include('./price-component') %> <%- include('./price-component') %>
          </div>
        </div>

        <!-- 商务舱区域 -->
        <div
          class="brand-options business-section"
          data-cabin-section="business"
          role="group"
          aria-label="Business class price options">
          <div class="price-options-grid">
            <%- include('./price-component') %> <%- include('./price-component') %> <%- include('./price-component') %>
          </div>
        </div>

        <% } %>
      </div>
    </div>
    <% }) %>
  </div>

  <%- include('../../../components/sz-modal/index', {id: 'flight-details', title: 'Transfer for 1 times', okText: 'Back'}) %>

  <div id="flight-details-content" role="dialog" aria-label="Flight details">
    <%- include('./flight-info', { className: 'modal' }) %>
    <div class="transfer-bar" role="separator" aria-label="Transfer information">
      <div class="transfer-line-left"></div>
      <div class="transfer-info">
        <span aria-label="Transfer to Shanghai, duration 1 hour 30 minutes">Transfer to Shanghai 1h30m</span>
      </div>
      <div class="transfer-line-right"></div>
    </div>
    <%- include('./flight-info', { className: 'modal' }) %>
  </div>
</div>
