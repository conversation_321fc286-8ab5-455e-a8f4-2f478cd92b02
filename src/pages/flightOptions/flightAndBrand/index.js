/**
 * Flight and Brand component
 * Handles the interaction between cabin options and brand expanded sections
 */

$(document).ready(function () {
  // Initialize cabin sections visibility
  initCabinSections();
  // Initialize the cabin option click handlers
  initCabinOptionHandlers();
  // Initialize multi-segment cabin option click handlers
  initMultiCabinOptionHandlers();
});

/**
 * Initialize cabin sections - hide all sections by default
 */
function initCabinSections() {
  // Hide all cabin sections on page load
  $('[data-cabin-section]').hide();
}

/**
 * Initialize click handlers for regular cabin options
 */
function initCabinOptionHandlers() {
  const cabinOptions = $('.flight-and-brand .cabin-option');

  cabinOptions.on('click', function () {
    const $this = $(this);
    const flightBrandItem = $this.closest('.flight-and-brand-item');
    const brandSection = flightBrandItem.find('[data-brand-section]');

    $this.siblings().removeClass('active');
    $this.toggleClass('active');

    if ($this.hasClass('active')) {
      const cabinType = $this.data('cabin-type');

      // Clear all other expanded states
      $('.brand-expanded').removeClass('active economy premium-economy business');
      $('.flight-and-brand-item').removeClass('expanded');

      // Hide all cabin sections first
      brandSection.find('[data-cabin-section]').removeClass('active').hide();

      // Show the brand section and add cabin type class
      brandSection.addClass('active ' + cabinType);
      flightBrandItem.addClass('expanded');

      // Show only the selected cabin section
      const targetSection = brandSection.find(`[data-cabin-section="${cabinType}"]`);
      if (targetSection.length) {
        targetSection.addClass('active').show();
      }
    } else {
      // Hide brand section and all cabin sections
      brandSection.removeClass('active economy premium-economy business');
      brandSection.find('[data-cabin-section]').removeClass('active').hide();
      flightBrandItem.removeClass('expanded');
    }
  });
}

/**
 * Initialize click handlers for multi-segment cabin options
 */
function initMultiCabinOptionHandlers() {
  const multiCabinOptions = $('.flight-and-brand .cabin-option-multi');

  multiCabinOptions.on('click', function () {
    const $this = $(this);
    const flightBrandItem = $this.closest('.flight-and-brand-item');
    const brandSection = flightBrandItem.find('[data-brand-section]');

    // Toggle active state
    const isCurrentlyActive = $this.hasClass('active');

    if (isCurrentlyActive) {
      // If currently active, collapse the section
      $this.removeClass('active');
      brandSection.removeClass('active economy premium-economy business');
      flightBrandItem.removeClass('expanded');
    } else {
      // Clear all other active states first
      $('.cabin-option-multi').removeClass('active');
      $('.brand-expanded').removeClass('active economy premium-economy business');
      $('.flight-and-brand-item').removeClass('expanded');

      // Activate current option
      $this.addClass('active');

      // Get cabin type from data attribute or class
      const cabinType = $this.data('cabin-type') || getCabinTypeFromClass($this);

      // Show brand expanded section with appropriate cabin type
      brandSection.addClass('active ' + cabinType);
      flightBrandItem.addClass('expanded');
    }
  });
}

/**
 * Get cabin type from element classes
 * @param {jQuery} $element - The cabin option element
 * @returns {string} - The cabin type (economy, premium-economy, business)
 */
function getCabinTypeFromClass($element) {
  if ($element.hasClass('economy')) {
    return 'economy';
  } else if ($element.hasClass('premium-economy')) {
    return 'premium-economy';
  } else if ($element.hasClass('business')) {
    return 'business';
  }
  return 'economy'; // default fallback
}
