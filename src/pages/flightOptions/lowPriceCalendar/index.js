// Import PriceCalendar if it's a module
// import PriceCalendar from './PriceCalendar.js';

// Or ensure PriceCalendar is loaded before this script
// Check if PriceCalendar is defined before using it
if (typeof PriceCalendar !== 'undefined') {
  const lowPriceCalendar = new PriceCalendar({
    container: '#low-price-calendar',
    // data: testData,
    // currency: 'CNY',
    onDateSelect: function (dateStr, dayData) {
      console.log('=== 日期选择调试信息 ===');
      console.log('原始日期字符串:', dateStr);
      console.log('日期数据:', dayData);

      // 使用this引用当前日历实例
      const parsedDate = this._parseDate(dateStr);
      console.log('解析后的日期对象:', parsedDate);
      console.log('日期对象详情:', {
        year: parsedDate.getFullYear(),
        month: parsedDate.getMonth() + 1,
        date: parsedDate.getDate(),
      });
      console.log('重新格式化的日期:', this.formatDate(parsedDate));
      console.log('========================');
    },
    onMonthChange: function (newMonth) {
      console.log('月份变化:', newMonth);
    },
  });
}
