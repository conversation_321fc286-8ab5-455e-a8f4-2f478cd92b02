@import '../../../less/variables.less';
@import '../../../less/mediaMixin.less';

.low-price-calendar {
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    font-size: 28px;
    font-weight: 500;
    color: @gray-5;

    .screenMobile({
      font-size: 20px;
      flex-direction: column;
      align-items: flex-start;
    });

    &-text {
      display: flex;
      align-items: center;

      font-size: 28px;
      font-weight: 400;

      .screenMobile({
        margin-top: 12px;
        font-size: 16px;
      });
    }

    &-i {
      margin-right: 6px;

      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 9999px;

      box-sizing: border-box;
      border: 2px solid @brand-1;
    }
  }

  .low-price-calendar-content {
    margin-top: 30px;

    .price-calendar-container {
      width: 100%;
      border: 1px solid #ccc;
      box-shadow: none;

      .price-calendar-header {
        display: none;
      }

      .price-calendar-footer {
        display: none;
      }

      .weekdays-header {
        margin-bottom: 60px;

        .screenPad({
          margin-bottom: 20px;
        });

        .screenMobile({
          margin-bottom: 10px;
        });
      }
    }
  }

  .low-price-calendar-footer {
    margin-top: 20px;
    font-size: 20px;
    color: @gray-5;

    .screenMobile({
      margin-top: 10px;
      font-size: 12px;
    });
  }
}
