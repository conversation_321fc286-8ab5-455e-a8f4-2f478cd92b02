@import '../../../less/mediaMixin.less';

.flight-segment {
  &-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    &-title {
      font-size: 40px;
      font-weight: 500;
      color: #3d3d3d;

      .screenPad({
        font-size: 30px;
      });

      .screenMobile({
        font-size: 20px;
      });
    }

    &-right {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-items: center;

      &-img {
        width: 23px;
        height: 23px;
      }

      &-text {
        font-size: 16px;
        color: @sub-3;
        margin-top: 4px;

        .screenMobile({
          display: none;
        });
      }
    }
  }

  // 往返航班选择器
  .flight-route-selector {
    display: flex;
    gap: 20px;

    .screenMobile({
      gap: 2px;
    });
  }

  .route-segment {
    flex: 1;
    position: relative;
    height: 80px;
    cursor: pointer;
    transition: all 0.3s ease;

    // 背景容器
    .segment-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 8px;
      overflow: hidden;

      // 渐变背景 - paint_23:7552 / paint_23:7584
      .segment-gradient {
        width: 100%;
        height: 100%;
        background: linear-gradient(180deg, #ffeded 0%, rgba(255, 237, 237, 0) 100%);
      }

      // 顶部红色条 - paint_5:1441 (状态色/red-2)
      .segment-top-bar {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 6px;
        background: @red-2; /* 状态色/red-2 */
        border-radius: 8px 8px 0 0;
      }
    }

    // 内容容器
    .segment-content {
      position: relative;
      z-index: 1;
      height: 100%;
      padding: 0 20px;
      display: flex;
      align-items: center;

      .screenMobile({
        padding: 0 10px;
      });
    }

    .segment-info {
      display: flex;
      align-items: center;
      gap: 20px;

      .screenPad({
        flex-direction: column;
        align-items: start;
        gap: 6px;
      });

      .screenMobile({
        flex-direction: column;
        align-items: start;
        gap: 6px;
      });
    }

    // 航段标签 (OUTBOUND/INBOUND)
    .segment-badge {
      padding: 0 7px;
      height: 20px;
      border-radius: 4px;
      background: @orange-3; /* 状态色/orange-3 */
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
      color: @gray-1_1; /* 中性色/grey-1.1 */
      line-height: 1;
    }

    // 路线信息
    .route-info {
      display: flex;
      align-items: center;
      gap: 20px;

      .screenMobile({
        gap: 4px;
      });

      .city-name {
        font-size: 20px;
        font-weight: 400;
        color: @gray-5; /* 中性色/gray-5 */
        line-height: 1;

        .screenMobile({
          font-size: 14px;
        });
      }

      .route-arrow {
        display: flex;
        align-items: center;
        width: 24px;
        height: 5px;
      }
    }

    // 激活状态
    &.active {
      .segment-background {
        .segment-gradient {
          background: linear-gradient(180deg, #ffeded 0%, rgba(255, 237, 237, 0) 100%);
        }

        .segment-top-bar {
          opacity: 1;
        }
      }

      .segment-badge {
        opacity: 1;
      }

      .city-name {
        color: @gray-5; /* 中性色/gray-5 */
      }

      .route-arrow svg path {
        fill: #3d3d3d;
      }
    }

    // 非激活状态
    &.inactive {
      opacity: 0.5;

      .segment-background {
        .segment-gradient {
          background: linear-gradient(
            180deg,
            rgba(255, 237, 237, 0.5) 0%,
            rgba(255, 237, 237, 0) 100%
          );
        }

        .segment-top-bar {
          opacity: 0.3;
        }
      }

      .segment-badge {
        opacity: 0.3;
      }

      .city-name {
        color: @sub-2; /* 辅色/sub-2 */
      }

      .route-arrow svg path {
        fill: @sub-2; /* 辅色/sub-2 */
      }

      .select-info {
        opacity: 0.3;
      }
    }
  }

  // ------------------ 多段 ------------------
  &.multi-city {
    .flight-route-selector {
      gap: 10px;

      .screenPad({
        gap: 6px;
      });

      .screenMobile({
        gap: 5px;
      });
    }

    .segment-content {
      padding-top: 20px;
      align-items: start;

      .screenPad({
        padding: 8px 10px 0;
      });

      .screenMobile({
        padding: 8px 5px 0;
      });
    }

    .segment-info {
      gap: 10px;
      flex-direction: column;
      align-items: start;

      .screenPad({
        gap: 6px;
      });
    }

    .route-info {
      gap: 10px;

      .screenPad({
        flex-direction: column;
        align-items: start;
        gap: 6px;

        .city-name {
          font-size: 12px;
        }
      });

      .screenMobile({
        display: none;
      });
    }

    .route-segment {
      height: 120px;

      .screenMobile({
        height: auto;
      });
    }

    .segment-badge {
      .screenPad({
        background: none;
        color: @gray-5;
        padding: 0;
      });

      .screenMobile({
        background: none;
        color: @gray-5;
        padding: 0;
        white-space: nowrap;
        
        font-size: 12px;
      });
    }

    .select-info {
      display: flex;
      align-items: center;
      gap: 10px;

      font-size: 14px;
      color: @gray-5;

      .screenPad({
        gap: 6px;

        flex-direction: column;
        align-items: start;

        font-size: 12px;
      });

      .screenMobile({
        display: none;
      });

      .amount {
        color: @brand-1;
        font-size: 12px;
      }
    }
  }

  // 悬停效果
  .route-segment:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  // 激活时的点击效果
  .route-segment:active {
    transform: translateY(0);
  }

  // 加载状态
  &.loading {
    .route-segment {
      pointer-events: none;
      opacity: 0.6;
    }
  }
}

// 动画定义
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 航班列表动画
.flight-list {
  animation: slideIn 0.3s ease-out;
}
