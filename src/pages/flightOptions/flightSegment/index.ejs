<%
    const _routeType =  typeof routeType !== 'undefined'? routeType  : 2;  // 1:单程 2:往返 3:多段
    const _title = typeof flightSegmentData !== 'undefined'? flightSegmentData[0].title  :'';
    const _flightSegmentData = typeof flightSegmentData !== 'undefined'? flightSegmentData: [];
%>

<div class="flight-segment <%= _routeType===3? 'multi-city':'' %>" role="region" aria-label="Flight segment selection">
  <div class="flight-segment-top">
    <h2 class="flight-segment-top-title" id="flight-segment-top-title"><%= _title %></h2>

    <button class="__text-button" aria-label="Open full price calendar">
      <div class="flight-segment-top-right">
        <img
          class="flight-segment-top-right-img"
          src="../../images/flightOptions/calendar.png"
          alt="Calendar icon"
        />

        <span class="flight-segment-top-right-text">Price calendar</span>
      </div>
    </button>
  </div>

  <div
    class="flight-route-selector"
    id="flight-route-selector"
    role="tablist"
    aria-label="Select flight direction"
  >
    <% _flightSegmentData.forEach(function(item, index){ %>
    <button
      class="route-segment <%= index === 0? 'active' : 'inactive' %>"
      role="tab"
      aria-selected="<%= index === 0? 'true' : 'false' %>"
      aria-label="<%= item.tagText %> route from <%= item.departure %> to <%= item.arrive %>"
      tabindex="<%= index === 0? '0' : '-1' %>"
      data-title="<%= item.title %>"
    >
      <div class="segment-background" aria-hidden="true">
        <div class="segment-gradient"></div>
        <div class="segment-top-bar"></div>
      </div>
      <div class="segment-content">
        <div class="segment-info">
          <div class="segment-badge outbound" aria-hidden="true"><%= item.tagText %></div>
          <div class="route-info">
            <span class="city-name" aria-label="Departure city"><%= item.departure %></span>
            <div class="route-arrow" aria-hidden="true">
              <svg
                width="24"
                height="5"
                viewBox="0 0 24 5"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                aria-hidden="true"
              >
                <path
                  d="M23.7419 4.2675C23.8075 4.64895 23.5152 4.99812 23.1281 5.00062L0.62313 5.00062C0.27811 5.00097 -0.00138 4.72064 5.1409e-06 4.37563C5.1409e-06 4.03 0.28313 3.75 0.62313 3.75L21.8294 3.75L19.1456 1.06437C18.9016 0.820509 18.9024 0.424701 19.1475 0.181875C19.3913 -0.061875 19.7913 -0.0587493 20.0294 0.18L23.5688 3.72188C23.7175 3.87063 23.775 4.075 23.7419 4.2675Z"
                  fill="#3D3D3D"
                />
              </svg>
            </div>
            <span class="city-name" aria-label="Arrival city"><%= item.arrive %></span>
          </div>

          <% if( _routeType === 3 && index === 0 ){ %>
          <div class="select-info" aria-label="Selected flight details">
            <div aria-label="Flight date and time">06-18 Wed 10:30</div>
            <div class="amount" aria-label="Price CNY 999">CNY999</div>
          </div>
          <% } %>
        </div>
      </div>
    </button>

    <% }) %>
  </div>
</div>
