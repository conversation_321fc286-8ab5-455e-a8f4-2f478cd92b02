/**
 * 航班段选择器交互逻辑
 */
class FlightSegmentSelector {
  constructor(container) {
    this.$container = $(container);
    this.$segments = $();
    this.activeIndex = 0;
    this.init();
  }

  init() {
    // 获取所有航段元素
    this.$segments = this.$container.find('.route-segment');

    // 绑定点击事件
    this.bindEvents();

    // 设置初始状态
    this.setActiveSegment(0);
  }

  bindEvents() {
    this.$segments.each((index, segment) => {
      $(segment).on('click', e => {
        e.preventDefault();
        this.setActiveSegment(index);
      });

      // 添加键盘支持
      $(segment).on('keydown', e => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          this.setActiveSegment(index);
        }
      });
    });
  }

  setActiveSegment(index) {
    if (index === this.activeIndex) return;

    // 移除所有激活状态
    this.$segments.removeClass('active').addClass('inactive').attr('aria-selected', 'false');
    // 设置新的激活状态
    const $activeSegment = this.$segments.eq(index);
    if ($activeSegment.length) {
      $activeSegment.removeClass('inactive').addClass('active').attr('aria-selected', 'true');
      this.activeIndex = index;

      // 更新顶部标题
      const title = $activeSegment.data('title');
      if (title) {
        $('#flight-segment-top-title').text(title);
      }

      // 触发自定义事件
      this.$container.trigger('segmentChange', {
        activeIndex: index,
        activeSegment: $activeSegment[0],
        title: title,
      });
    }
  }
  getActiveIndex() {
    return this.activeIndex;
  }

  getActiveSegment() {
    return this.$segments.eq(this.activeIndex)[0];
  }
}

// 自动初始化
$(document).ready(() => {
  $('#flight-route-selector').each((_, selector) => {
    new FlightSegmentSelector(selector);
  });
});

// 导出供外部使用
window.FlightSegmentSelector = FlightSegmentSelector;
