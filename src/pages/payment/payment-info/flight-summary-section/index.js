$(document).ready(function () {
  // 退改签条目点击切换逻辑
  $('.refund-change-item').on('click', function () {
    // 获取目标区域的类名
    const targetSection = $(this).attr('data-target');
    const $targetElement = $(this).closest('.flight-summary-section').find(`.${targetSection}`);
    const $arrowIcon = $(this).find('.arrow-icon');

    // 如果目标元素不存在则返回
    if ($targetElement.length === 0) return;

    // 检查当前区域是否可见
    const isVisible = $targetElement.is(':visible');
    if (isVisible) {
      // 隐藏当前区域
      $targetElement.hide();
      $arrowIcon.removeClass('rotated');
    } else {
      // 先隐藏所有其他区域
      $('.section-content').hide();

      // 重置所有箭头图标
      $('.refund-change-item .arrow-icon').removeClass('rotated');

      // 显示目标区域并旋转对应箭头
      $targetElement.show();
      $arrowIcon.addClass('rotated');
    }
  });

  const tableByRefund1 = new SZTable({
    container: '#table-payment-info-refund-1',
    columns: [
      {
        title: 'Change of Flight/Cabin Class',
        dataIndex: 'time',
        key: 'time',
      },
      {
        dataIndex: 'isUse',
        key: 'isUse',
      },
      {
        title: 'Refund',
        dataIndex: 'amount',
        key: 'amount',
      },
    ],
    dataSource: [
      {
        key: '1',
        time: 'After Takeoff',
        isUse: 'All Unused',
        amount: 'CNY660.0',
      },
      {
        key: '1',
        time: '',
        isUse: 'Partially Used',
        amount: 'CNY660.0',
      },
      {
        key: '1',
        time: 'Before Takeoff',
        isUse: 'All Unused',
        amount: 'CNY660.0',
      },
      {
        key: '1',
        time: '',
        isUse: 'Partially Used',
        amount: 'CNY660.0',
      },
    ],
  });
  tableByRefund1.setMergedCells([
    { row: 0, col: 0, rowspan: 2, colspan: 1 },
    { row: 2, col: 0, rowspan: 2, colspan: 1 },
  ]);
  tableByRefund1.setMergedHeaders([{ col: 0, colspan: 2 }]);

  const tableByRefund2 = new SZTable({
    container: '#table-payment-info-refund-2',
    columns: [
      {
        title: 'Change of Flight/Cabin Class',
        dataIndex: 'time',
        key: 'time',
      },
      {
        dataIndex: 'isUse',
        key: 'isUse',
      },
      {
        title: 'Refund',
        dataIndex: 'amount',
        key: 'amount',
      },
    ],
    dataSource: [
      {
        key: '1',
        time: 'After Takeoff',
        isUse: 'All Unused',
        amount: 'CNY661.0',
      },
      {
        key: '1',
        time: '',
        isUse: 'Partially Used',
        amount: 'CNY661.0',
      },
      {
        key: '1',
        time: 'Before Takeoff',
        isUse: 'All Unused',
        amount: 'CNY661.0',
      },
      {
        key: '1',
        time: '',
        isUse: 'Partially Used',
        amount: 'CNY661.0',
      },
    ],
  });
  tableByRefund2.setMergedCells([
    { row: 0, col: 0, rowspan: 2, colspan: 1 },
    { row: 2, col: 0, rowspan: 2, colspan: 1 },
  ]);
  tableByRefund2.setMergedHeaders([{ col: 0, colspan: 2 }]);

  const tableByChange1 = new SZTable({
    container: '#table-payment-info-change-1',
    columns: [
      {
        title: 'Change of Flight/Cabin Class',
        dataIndex: 'time',
        key: 'time',
      },
      {
        dataIndex: 'isUse',
        key: 'isUse',
      },
      {
        title: 'Change',
        dataIndex: 'amount',
        key: 'amount',
      },
    ],
    dataSource: [
      {
        key: '1',
        time: 'After Takeoff',
        isUse: 'All Unused',
        amount: 'CNY660.0',
      },
      {
        key: '1',
        time: '',
        isUse: 'Partially Used',
        amount: 'CNY660.0',
      },
      {
        key: '1',
        time: 'Before Takeoff',
        isUse: 'All Unused',
        amount: 'CNY660.0',
      },
      {
        key: '1',
        time: '',
        isUse: 'Partially Used',
        amount: 'CNY660.0',
      },
    ],
  });
  tableByChange1.setMergedCells([
    { row: 0, col: 0, rowspan: 2, colspan: 1 },
    { row: 2, col: 0, rowspan: 2, colspan: 1 },
  ]);
  tableByChange1.setMergedHeaders([{ col: 0, colspan: 2 }]);

  const tableByChange2 = new SZTable({
    container: '#table-payment-info-change-2',
    columns: [
      {
        title: 'Change of Flight/Cabin Class',
        dataIndex: 'time',
        key: 'time',
      },
      {
        dataIndex: 'isUse',
        key: 'isUse',
      },
      {
        title: 'Change',
        dataIndex: 'amount',
        key: 'amount',
      },
    ],
    dataSource: [
      {
        key: '1',
        time: 'After Takeoff',
        isUse: 'All Unused',
        amount: 'CNY661.0',
      },
      {
        key: '1',
        time: '',
        isUse: 'Partially Used',
        amount: 'CNY661.0',
      },
      {
        key: '1',
        time: 'Before Takeoff',
        isUse: 'All Unused',
        amount: 'CNY661.0',
      },
      {
        key: '1',
        time: '',
        isUse: 'Partially Used',
        amount: 'CNY661.0',
      },
    ],
  });
  tableByChange2.setMergedCells([
    { row: 0, col: 0, rowspan: 2, colspan: 1 },
    { row: 2, col: 0, rowspan: 2, colspan: 1 },
  ]);
  tableByChange2.setMergedHeaders([{ col: 0, colspan: 2 }]);
});
