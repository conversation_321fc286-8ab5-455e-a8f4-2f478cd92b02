/**
 * 支付信息页面交互逻辑
 * 处理支付方式切换、行程单选项选择等交互功能
 */

$(document).ready(function () {
  // 下一步按钮点击事件
  $('.next-button').on('click', function () {
    // 处理下一步逻辑
    console.log('点击了下一步按钮');
    const values = getFormValues($('.i-form-content'));
    console.log('fffff', values);
  });

  // 扫码支付弹窗
  const modalScanPay = new SZModal({
    modalDom: $('#scan-pay-modal'),
    contentDom: $('#modal-content-scan-pay'),
    onOk: () => {
      console.log('ok');
    },
  });
  // modalScanPay.open();

  // 在浏览器中信用卡、储蓄卡支付页面跳转新增，但因支付失败或其他原因将新增页面删除后，回到原支付界面出现的弹窗
  const modalDebitCard = new SZModal({
    modalDom: $('#debit-card-modal'),
    contentDom: $('#modal-content-debit-card'),
    onOk: () => {
      console.log('ok');
    },
  });
  // modalDebitCard.open();
});
