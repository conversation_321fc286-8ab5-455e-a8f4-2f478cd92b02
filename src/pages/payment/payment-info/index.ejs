<%
  const flightSummaryData1 = {
    tableRefundId: 'table-payment-info-refund-1',
    tableRefundNotes: [
    'When the refund, change and rescheduling rules for multiple flight segments differ, the strictest standard shall be applied.',
    'Refunds must be requested within one year from the start date of the first - leg journey (if the first flight segment of the ticket remains unused, it is calculated from the date of ticket issuance). No refund will be processed after the expiration of this period. For tickets that do not allow refunds, the fuel surcharge and other related fees will not be refunded.',
    ],
    tableChangeId: 'table-payment-info-change-1',
    tableChangeNotes: [
      'When the change, change and rescheduling rules for multiple flight segments differ, the strictest standard shall be applied.',
      'Refunds must be requested within one year from the start date of the first - leg journey (if the first flight segment of the ticket remains unused, it is calculated from the date of ticket issuance). No change will be processed after the expiration of this period. For tickets that do not allow refunds, the fuel surcharge and other related fees will not be refunded.',
    ],
  }

  const flightSummaryData2 = {
    tableRefundId: 'table-payment-info-refund-2',
    tableChangeId: 'table-payment-info-change-2',
    tableRefundNotes: [
    'When the refund, change and rescheduling rules for multiple flight segments differ, the strictest standard shall be applied.',
    'Refunds must be requested within one year from the start date of the first - leg journey (if the first flight segment of the ticket remains unused, it is calculated from the date of ticket issuance). No refund will be processed after the expiration of this period. For tickets that do not allow refunds, the fuel surcharge and other related fees will not be refunded.',
    ],
    tableChangeNotes: [
      'When the change, change and rescheduling rules for multiple flight segments differ, the strictest standard shall be applied.',
      'Refunds must be requested within one year from the start date of the first - leg journey (if the first flight segment of the ticket remains unused, it is calculated from the date of ticket issuance). No change will be processed after the expiration of this period. For tickets that do not allow refunds, the fuel surcharge and other related fees will not be refunded.',
    ],
  }
%>

<div class="payment-info">
  <%- include('./flight-summary-section/index', flightSummaryData1 ) %> <%-
  include('./flight-summary-section/index',flightSummaryData2 ) %> <%- include('./passenger-info-section/index' ) %> <%-
  include('./additional-services-section/index') %> <%- include('./contact-info-section/index') %> <%-
  include('./payment-method-section/index') %> <%- include('./collect-itinerary-section/index') %>

  <button class="next-button" id="next-btn">Next</button>

  <%- include('./scan-pay-modal', {id: 'scan-pay-modal'}) %>
  <%- include('./debit-card-modal',{id:'debit-card-modal'}) %>
</div>
