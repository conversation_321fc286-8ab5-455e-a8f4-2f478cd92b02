<%
  const Security_Code = 'Security Code';
%>

<div>
  <div class="i-form-content">
    <div class="form-row i-card">
      <%- include('../../../../components/sz-select/index',{ label:'Card Types', required:true, className:"card-type",
      formName:'cardType', options:[ { label:'Male', value:'male', }, { label:'Female', value:'female', }, ], }) %>

      <div class="i-card-type">
        <img src="../../../../images/payment/i-pay-type.png" />
      </div>
    </div>

    <div class="card-tit">Payment Card Information</div>
    <div class="form-row">
      <%- include('../../../../components/sz-input/input',{ label:'Transaaction Card Number', required:true,
      formName:'cardNumber', }) %> <%- include('../../../../components/sz-datepicker/index',{ label:'Date of birth',
      required:true, formName:'dateBirth', }) %> <%- include('../../../../components/sz-input/input', {
      label:Security_Code, errorMsg:'请输入', required:true, formName:'securityCode', }) %>
    </div>

    <div class="card-tit">Cardholders Information</div>
    <div class="form-row">
      <%- include('../../../../components/sz-input/input',{ label:'First Name', required:true, formName:'fName', }) %>
      <%- include('../../../../components/sz-input/input',{ label:'Last Name', required:true, formName:'lName', }) %>
      <%- include('../../../../components/sz-input/input',{ label:'Passport Number', errorMsg:'请输入', required:true,
      formName:'passportNumber', }) %>

      <%- include('../../../../components/sz-input/input',{ label:'Phone Number', required:true, formName:'phoneNumber',
      }) %> <%- include('../../../../components/sz-input/input',{ label:'Email', errorMsg:'请输入', required:true,
      formName:'email', }) %>
    </div>
    <div class="form-row">

    </div>

    <div class="card-tit">Billing Address</div>
    <div class="form-row">
      <%- include('../../../../components/sz-input/input',{ label:'City', required:true, formName:'city', }) %> <%-
      include('../../../../components/sz-input/input',{ label:'State', required:true, formName:'state', }) %> <%-
      include('../../../../components/sz-input/input',{ label:'Country', errorMsg:'请输入', required:true,
      formName:'country', }) %>

      <%- include('../../../../components/sz-input/input',{ className:'street-address', label:'Street Address',
      required:true, formName:'streetAddress', }) %>


      <%- include('../../../../components/sz-input/input',{ label:'Postaal Code', required:true, formName:'postalCode',
      }) %> <%- include('../../../../components/sz-input/input',{ label:'Email', errorMsg:'请输入',
      formName:'addressEmail', }) %>
    </div>

    <div class="privacy-agreement-con">
      <label class="checkbox-container">
        <input type="checkbox" id="privacy-agreement" class="privacy-agreement-ipt" name="privacy-agreement" required />
        <span class="checkmark"></span>
        <span class="agreement-text" tabindex="0">
          I have read and understood it
          <a href="#" class="privacy-link">Note for Purchase,</a>
          <a href="#" class="privacy-link">Privacy Notice</a>
        </span>
      </label>
      <div class="privacy-tips">
        <p>
          Please carefully read the refund, change, and reschedule regulations to understand the rules for the ticket
          you have booked. If the ticket supports refund or rescheduling, certain fees may be charged; if not, refund or
          rescheduling is not allowed.
        </p>
        <p>
          To ensure transaction security, please confirm that this operation is conducted by the cardholder 本人 or with
          the cardholder's authorization.
        </p>
        <p>
          To protect the cardholder's rights and prevent fraudulent transactions, we may request the passenger to
          provide original photos of the bank card used for online booking and the cardholder's identification at any
          time before or 
        </p>
      </div>
    </div>
  </div>
  <div class="payment-notice">
    1. You are making a payment with an international credit card. If the currency of your bank card differs from the
    payment currency, the payment provider will convert it at the real-time exchange rate. Any additional fees incurred
    are your responsibility and will not be charged by Shenzhen Airlines. Please be aware of this.
    <br />
    2. Using an international credit card for payment may incur cross-border transaction fees. These fees are not
    charged by Shenzhen Airlines. For details, please consult your card-issuing bank.
    <br />
    3. To ensure your itinerary is used normally, we may contact you by phone after your booking is completed. Please
    note Shenzhen Airlines' only customer service hotline: 95361.
    <br />
    4. If you have any questions during the booking process, please call our customer service hotline:
    <br />
    95361-2 (Mainland China)
    <br />
    0086-755-95361 or 0755-******** (International)
  </div>
</div>
