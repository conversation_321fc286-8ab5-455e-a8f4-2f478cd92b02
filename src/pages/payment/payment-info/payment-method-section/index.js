$(document).ready(function () {
  // PC版支付方式选项切换
  $('.payment-option').on('click', function () {
    $('.payment-option').removeClass('active');
    $(this).addClass('active');

    // 重置所有银行选项的选中状态
    $('.bank-option').removeClass('selected');

    // 获取当前选中的支付方式类型
    const targetClass = $(this).data('payment-type');

    // 隐藏所有支付银行选项
    $('.payment-banks').hide();

    // 显示对应的支付银行选项
    if (targetClass) {
      $(`.payment-banks.${targetClass}`).css('display', 'flex');
    }
  });

  // 移动端支付方式选项切换
  $('.payment-section-header').on('click', function () {
    const $section = $(this).closest('.payment-section');

    // 切换当前section的展开状态
    $section.toggleClass('active');

    // 如果当前section被激活，收起其他sections
    if ($section.hasClass('active')) {
      $('.payment-section').not($section).removeClass('active');

      // 重置所有银行选项的选中状态
      $('.bank-option').removeClass('selected');
    }
  });

  // 银行选项点击切换
  $('.bank-option').on('click', function () {
    // 移除所有银行选项的选中状态
    $('.bank-option').removeClass('selected');

    // 为当前点击的选项添加选中状态
    $(this).addClass('selected');
  });
});
