@import '../../../less/variables.less';
@import '../../../less/mediaMixin.less';

@import '../../flightOptions/flightAndBrand/index.less';

.payment-info,
.payment-result-info {
  margin: 0 auto;

  .section {
  }

  .section-title {
    font-size: 24px;
    color: #3d3d3d;
    margin: 20px 0;
    font-weight: 400;

    .screenMobile({
      font-size: 16px;
      margin: 20px 0 15px;
    });
  }

  // Section 1: Flight Summary
  .flight-summary-section {
    margin-bottom: 30px;

    .screenMobile({
      margin-bottom: 20px;
    });

    .flight-info {
      border: 1px solid #ccc;
      border-radius: 8px;
      padding: 20px;

      .screenMobile({
        padding: 10px;
      });
    }

    .flight-route {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }

    .journey-tag {
      background: @orange-3;
      color: @gray-1_1;
      padding: 4px 7px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
    }

    .flight-date {
      font-size: 16px;
      color: @gray-5;
      margin-left: 20px;

      .screenMobile({
        font-size: 12px;
      });
    }

    .flight-cities {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .city-name {
      font-size: 28px;
      color: @gray-5;
      font-weight: 500;

      .screenMobile({
        font-size: 20px;
      });
    }

    .flight-icon {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .flight-line {
      width: 144px;
      height: 1px;
      background: @gray-5;
      margin: 10px 0;

      .screenMobile({
        width: 36px;
      });
    }

    .flight-time {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .departure-time,
    .arrival-time {
      font-size: 24px;
      color: @gray-5;

      .screenMobile({
        font-size: 16px;
      });
    }

    .btns {
      display: flex;
      justify-content: flex-end;
      margin-top: 10px;
    }

    .refund-change {
      display: flex;
      align-items: center;
      gap: 30px;
    }

    .refund-change-item {
      display: flex;
      align-items: center;
      color: @brand-2;
      font-size: 16px;
      text-decoration: underline;
      cursor: pointer;
      transition: background-color 0.2s ease;
      // padding: 8px 12px;
      border-radius: 4px;
      margin: 2px 0;

      // &:hover {
      // background-color: rgba(0, 0, 0, 0.05);
      // }

      .screenMobile({
        font-size: 12px;
      });
    }

    .arrow-icon {
      width: 16px;
      height: 16px;
      margin-left: 4px;
      transition: transform 0.3s ease;
      transform-origin: center;
      transform: rotate(-180deg);

      &.rotated {
        transform: rotate(0deg);
      }

      .screenMobile({
        width: 12px;
        height: 12px;
      });
    }

    // Section content animation for clickable sections
    .section-content {
      transition: all 0.3s ease;
      overflow: hidden;

      border: 1px solid @brand-1;
      padding: 20px;

      .screenMobile({
        padding: 10px;
      });
    }
  }

  // Section 2: Passenger Information
  .passenger-info-section {
    .passenger-card {
      background: #f4f6f9;
      border: 1px solid #cccccc;
      border-radius: 8px;
      padding: 30px 20px 20px;
      margin-bottom: 30px;
      position: relative;

      .screenMobile({
      padding: 20px 10px;
        margin-bottom: 20px;
      });
    }

    .passenger-header {
      position: absolute;
      min-width: 124px;
      height: 24px;
      top: -8px;
      left: 16px;

      background-image: url('../../images/payment/psg.svg');
      background-repeat: no-repeat;

      display: flex;
      align-items: center;
      justify-content: center;
    }

    .passenger-header span {
      color: @gray-0;
      font-size: 14px;
      font-weight: 500;
    }

    .passenger-info {
      display: flex;
      gap: 20px;

      .screenPad({
        gap: 20px;
        flex-wrap: wrap;
      });

      .screenMobile({
        gap: 10px;
        flex-wrap: wrap;
      });
    }

    .info-item {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .screenPad({
        width: calc(33% - 14px);
      });

      .screenMobile({
        width: calc(50% - 5px);
      });
    }

    .info-label {
      font-size: 14px;
      color: @gray-5;
    }

    .info-value {
      display: flex;
      gap: 5px;
      word-break: break-all;
      white-space: normal;

      font-size: 16px;
      color: @gray-5;
      font-weight: 500;
    }

    .info-value.highlight {
      color: @brand-4;
      white-space: nowrap;
    }
  }

  // Section 3: Additional Services
  .additional-services-section {
    .service-card {
      margin-bottom: 30px;
      overflow: hidden;

      .screenMobile({
        margin-bottom: 20px;
      });
    }

    .service-header {
      display: flex;
      align-items: center;
      background: linear-gradient(180deg, #f4f6f9 0%, rgba(255, 255, 255, 0) 100%);
      border: 1px solid @gray-1;
      border-radius: 8px 8px 0 0;
      border-bottom: none;
      padding: 20px;
      overflow: hidden;

      .screenMobile({
        padding: 10px;
      });
    }

    .service-icon {
      width: 20px;
      height: 20px;
      margin-right: 10px;

      img {
        width: 100%;
        height: 100%;
        vertical-align: unset;
      }
    }

    .service-title {
      font-size: 16px;
      color: @sub-4;
      font-weight: 500;
    }

    .service-body {
      border: 1px solid @gray-1;
      border-top: none;
      padding: 20px;
      border-radius: 0 0 8px 8px;

      .screenMobile({
        padding: 10px;
      });
    }

    .passenger-service {
      margin-bottom: 25px;

      .screenMobile({
        margin-bottom: 10px;
      });

      .flight-info-row:nth-child(2n + 1) {
        background: @gray-0;
      }
    }

    .passenger-name {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      .screenMobile({
        margin-bottom: 10px;
      });
    }

    .passenger-avatar {
      width: 26px;
      height: 26px;
      border-radius: 50%;
      background: #9dd0d1;
      display: flex;
      justify-content: center;
      align-items: center;
      color: @gray-0;
      font-size: 14px;
      font-weight: 500;
      margin-right: 10px;
    }

    .passenger-name-text {
      font-size: 20px;
      color: @gray-5;
      font-weight: 500;

      .screenMobile({
        font-size: 14px;
      });
    }

    .flight-info-row {
      display: flex;
      background: #f9f9f9;
      border-radius: 8px;
      padding: 10px 20px;

      .screenMobile({
        padding: 10px;
        align-items: center;
      });
    }

    .panel-header-text {
      font-size: 16px;
      color: @gray-5;

      .screenMobile({
        font-size: 14px;
      });
    }

    .flight-info-left,
    .flight-info-right {
      display: flex;
      flex: 1;
    }

    .flight-info-left {
      .screenMobile({
        flex-wrap: wrap;
      });
    }

    .flight-info-item {
      display: flex;
      align-items: center;
      margin-right: 40px;
      white-space: nowrap;

      .screenMobile({
        margin-right: 10px;
      });
    }

    .flight-code {
      font-size: 16px;
      color: @gray-5;
      margin: 0 15px;
    }

    .seat-number {
      background: @orange-3;
      color: #f4f6f9;
      padding: 1px 7px;
      border-radius: 4px;
      font-size: 16px;

      .screenMobile({
        font-size: 14px;
      });
    }

    .service-price {
      flex: 1;
      text-align: right;
      font-size: 16px;
      color: @gray-5;
      white-space: nowrap;

      .screenMobile({
        flex: 0;
        font-size: 14px;
      });
    }

    .subtotal {
      text-align: right;
      font-size: 16px;
      color: @brand-4;
      font-weight: 500;

      .screenMobile({
        font-size: 14px;
      });
    }
  }

  // Section 4: Contact Information
  .contact-info-section {
    .contact-info {
      display: flex;
      flex-wrap: wrap;
      background: #f4f6f9;
      border: 1px solid #ccc;
      border-radius: 8px;
      padding: 20px;

      .screenMobile({
        padding: 10px;
        gap: 10px 0px;
      });
    }

    .contact-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 10px;

      .screenMobile({
        flex:auto;
        width: 50%;
        gap: 5px;
      });
    }

    .contact-label {
      font-size: 16px;
      color: @gray-5;

      .screenMobile({
        font-size: 12px;
      });
    }

    .contact-value {
      font-size: 20px;
      color: @gray-5;
      font-weight: 500;

      .screenPad({
        font-size: 16px;
      });

      .screenMobile({
        font-size: 14px;
      });
    }
  }

  // Section 5: Payment Method
  .payment-method-section {
    .payment-methods {
      border: 1px solid #ccc;
      border-radius: 8px;
      padding: 20px;

      .screenPad({
        padding: 10px;
      });

      .screenMobile({
        padding: 10px;
      });
    }

    .payment-notice {
      background: #fdf7e8;
      border: 1px solid #eeb71c;
      padding: 10px 20px;
      margin-bottom: 30px;
      font-size: 14px;
      color: @gray-5;
      line-height: 1.2;

      .screenMobile({
        padding: 10px;
        margin-bottom: 20px;
      });
    }

    .payment-options {
      display: flex;
      margin-bottom: 30px;
      background: @gray-1_1;
      border-radius: 8px;
      overflow: hidden;
    }

    .payment-option {
      flex: 1;
      padding: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      color: @gray-4;
      font-weight: 500;
      cursor: pointer;
      border: 1px solid transparent;
    }

    .payment-option.active {
      background: linear-gradient(180deg, #cc0100 0%, rgba(204, 1, 0, 0.5) 100%);
      color: @gray-0;
      position: relative;
      border-radius: 8px;
      overflow: hidden;
    }

    .payment-option.active::after {
      content: '';
      position: absolute;
      right: 10px;
      bottom: -15px;
      width: 65px;
      height: 55px;

      background-image: url('../../images/payment/bg-logo.svg');
      background-repeat: no-repeat;
      background-size: contain;
      background-position: center;
    }

    .payment-banks {
      display: flex;
      gap: 25px;
      flex-shrink: 0;
      flex-wrap: wrap;

      // 默认隐藏所有支付银行选项
      &:not(.qr-code) {
        display: none;
      }
    }

    .bank-option {
      width: calc((100% - (25px * 4)) / 5);
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #ccc;
      border-radius: 8px;
      position: relative;
      cursor: pointer;
      transition: border-color 0.2s ease;
      overflow: hidden;

      // 默认状态 - 右下角显示未选中图标
      &::after {
        content: '';
        position: absolute;
        bottom: 0px;
        right: 0px;
        width: 34px;
        height: 34px;
        background-image: url('../../images/payment/unselect-payment.svg');
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
      }

      &:hover {
        border-color: @brand-2;
      }

      // 激活状态 - 右下角显示选中图标，边框变色
      &.selected {
        border-color: @brand-2;

        &::after {
          background-image: url('../../images/payment/select-payment.svg');
        }
      }
    }

    .pc {
      .screenPad({
        display: none;
      });

      .screenMobile({
        display: none;
      });
    }

    .pad-and-mobile {
      display: none;

      .screenPad({
        display: block;
      });

      .screenMobile({
        display: block;
      });

      // 移动端支付选项样式
      .payment-options-mobile {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .screenMobile({
          gap: 10px;
        });
      }

      .payment-section {
        border-radius: 8px;
        background: #ffffff;
        overflow: hidden;

        &.active {
          .payment-section-content {
            display: block;
          }
        }
      }

      .payment-section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        cursor: pointer;
        background: #ffffff;
      }

      .payment-section-title {
        font-size: 16px;
        font-weight: 500;
        color: @gray-4; /* token: 中性色/gray-4 */

        .screenMobile({
          font-size: 12px;
        });
      }

      .expand-icon {
        width: 18px;
        height: 18px;
        background-image: url('../../images/additionalServices/circle.svg');
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
      }

      // 当 payment-section 有 active 类时，切换图标
      .payment-section.active .expand-icon {
        background-image: url('../../images/additionalServices/active-circle.svg');
      }

      .payment-section-content {
        display: none;
        padding: 10px;

        .screenMobile({
          padding: 0 10px;
        });

        .payment-banks {
          display: flex;
          flex-wrap: wrap;
          gap: 20px;
          padding: 0;

          .screenMobile({
            gap: 10px;
          });

          &.qr-code,
          &.credit-card,
          &.debit-card,
          &.third-party {
            .bank-option {
              width: calc(33.333% - 14px);

              .screenMobile({
                width: calc(50% - 5px);
              });
            }
          }

          &.international-card {
            .bank-option {
              width: 100%;
            }
          }
        }

        .bank-option {
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #cccccc;
          border-radius: 8px;
          position: relative;
          cursor: pointer;
          transition: border-color 0.2s ease;
          background: #ffffff;

          .screenMobile({
            height: 55px;
          });

          // 默认状态 - 右下角显示未选中图标
          &::after {
            content: '';
            position: absolute;
            bottom: 0px;
            right: 0px;
            width: 34px;
            height: 34px;
            background: #cccccc; /* token: 中性色/gray-2 */
            border-radius: 0 0 6px 0;
            mask: url("data:image/svg+xml,%3Csvg width='34' height='34' viewBox='0 0 34 34' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 34L34 34L34 0L0 34ZM21.8521 29.1833L19.8333 31.1667L17.8146 29.1479L14.1667 25.5L16.1854 23.4813L19.8333 27.1646L29.1479 17.85L31.1667 19.8687L21.8521 29.1833Z' fill='%23CCCCCC'/%3E%3C/svg%3E")
              no-repeat center;
            mask-size: contain;

            .screenMobile({
              width: 23px;
              height: 23px;
            });
          }

          &:hover {
            border-color: #cc0100; /* token: 品牌色/brand-1 */
          }

          // 激活状态 - 右下角显示选中图标，边框变色
          &.selected {
            border-color: #cc0100; /* token: 品牌色/brand-1 */

            &::after {
              background: #cc0100; /* token: 品牌色/brand-1 */
            }
          }

          img {
            max-width: 80%;
            max-height: 60%;
            object-fit: contain;
          }
        }
      }
    }
  }

  // Section 6: Collect Itinerary
  .collect-itinerary-section {
    .itinerary-options {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .payment-notice {
      margin-top: 40px;
      background: #fdf7e8;
      border: 1px solid #eeb71c;
      padding: 10px 20px;
      margin-bottom: 30px;
      font-size: 14px;
      color: @gray-5;
      line-height: 1.2;
    }

    .itinerary-option {
      background: @gray-0;
      border: 1px solid #cc0100;
      border-radius: 8px;
      padding: 30px;
      display: flex;
      align-items: center;
      cursor: pointer;

      .screenPad({
        padding: 20px;
      });

      .screenMobile({
        padding: 15px;
      });
    }

    .itinerary-option.default {
      border-color: #cccccc;
    }

    .radio-button {
      width: 16px;
      height: 16px;
      border: 1px solid @gray-1;
      border-radius: 50%;
      margin-right: 10px;
      position: relative;
    }

    .radio-button.selected {
      border-color: @gray-1;
    }

    .radio-button.selected::after {
      content: '';
      position: absolute;
      width: 8.6px;
      height: 8.6px;
      background: @brand-4;
      border-radius: 50%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .itinerary-text {
      flex: 1;
      font-size: 16px;
      color: #4f3d1e;

      .screenMobile({
        font-size: 14px;
      });
    }

    .itinerary-text .highlight {
      color: @brand-3;
    }

    .itinerary-text .underline {
      text-decoration: underline;
    }
  }

  // Total cost
  .total-cost {
    text-align: right;
    font-size: 20px;
    color: @brand-1;
    font-weight: 500;
    margin: 30px 0;

    .screenMobile({
      margin: 20px 0;

      font-size: 14px;
    });
  }

  .mailed-itinerary-option.active {
    border-bottom: none;
    border-radius: 8px 8px 0 0;
  }

  .itinerary-form-card {
    display: none;
    background: @gray-0;
    border: 1px solid #cc0100;
    border-width: 0 1px 1px;
    border-radius: 0 0 8px 8px;
    padding: 0 30px 30px;
  }

  // Next button
  .next-button {
    background: @brand-1;
    color: @gray-0;
    border: none;
    border-radius: 8px;
    height: 52px;
    width: 230px;
    font-size: 20px;
    font-weight: 500;
    cursor: pointer;
    float: right;
    margin-top: 30px;

    .screenMobile({
      width: 100%;
    });
  }

  // .next-button:hover {
  //   background: @brand-1;
  // }

  .i-form-content,
  .mail-form-content {
    width: 100%;

    // 表单行
    .form-row {
      display: flex;
      margin-bottom: 20px;
      flex-wrap: wrap;

      &.i-card {
        flex-wrap: nowrap;
        .screenMobile({
          flex-direction: row;
        });
      }

      .screenMobile({
        flex-direction: column;
        gap: 15px;
        margin-bottom: 15px;
      });

      .screenPad({
        margin-bottom: 0;
      });

      &:last-child {
        margin-bottom: 0;
      }
    }

    // 表单组（半宽）
    .form-item {
      width: calc(33.3333333% - 13.3333px);
      padding-bottom: 20px;
      margin-right: 20px;

      &:nth-child(3n) {
        margin-right: 0;
        .screenPad({
          margin-right: 20px;
        });
      }

      &:nth-child(2n) {
        .screenPad({
          margin-right: 0;
        });
      }

      .screenPad({
        width: calc(50% - 20px);
      });
      .screenMobile({
        width: 100%;
      });
    }
  }

  .mail-form-content {
    .courier-company {
      width: 100%;
    }
  }
  // 国际支付表单内容
  .i-form-content {
    padding: 24px 0;

    .screenMobile({
      padding: 16px 15px;
    });

    .card-tit {
      margin: 32px 0 24px 0;
      font-size: 24px;
    }

    .card-type {
      width: 80%;
    }
    .card-type-img {
      flex: 1;
    }
    .street-address {
      width: 100%;
    }

    .input-single .label-wrap {
      overflow: visible;
      .bubble-info {
        width: 200px;
        left: 0;
        margin-left: -30px;
        .screenMobile({
          width: 300px;
          white-space: wrap;
          margin-left: 0;
        });
        .screenMobile({
          width: 300px;
          white-space: wrap;
          margin-left: 0;
        });
      }
    }
  }

  .privacy-agreement-con {
    font-size: 16px;
    border-top: 1px solid @gray-2;
    border-bottom: 1px solid @gray-2;
    padding: 24px 0;
    .privacy-agreement-ipt {
      width: 16px;
      height: 16px;
    }

    .privacy-tips {
      margin-top: 30px;
      margin-left: 20px;
      p {
        line-height: 1.5;
      }
    }
  }
}

.payment-info-tippy {
  .psg-subtotal {
    width: 690px;
    max-width: 100%;

    .screenMobile({
      width: 305px;
      max-width: 100%;
    });

    &-item {
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:nth-child(n + 2) {
        margin-top: 10px;
      }
    }
  }
}

.scan-pay-modal {
  .modal-content-scan-pay {
    .scan-pay-container {
      background-color: #ffffff; /* 中性色/gray-0 */
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 30px;
      box-sizing: border-box;

      .screenMobile({
        gap: 20px;
      });

      /* 二维码区域 */
      .qr-code-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;

        .screenMobile({
          gap: 10px;
        });
      }

      .qr-code-wrapper {
        width: 320px;
        height: 320px;
        display: flex;
        justify-content: center;
        align-items: center;

        .screenMobile({
          width: 190px;
          height: 190px;
        });
      }

      .qr-code-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      /* 金额显示 */
      .amount-display {
        font-size: 28px;
        font-weight: 500; /* Medium */
        color: #cc0100; /* 品牌色/brand-1 */
        text-align: left;

        .screenMobile({
          font-size: 20px;
        });
      }

      /* 扫码提示 */
      .scan-tip {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: 8px;
      }

      .scan-tip span {
        font-size: 28px;
        font-weight: 500; /* Medium */
        color: #101010; /* 中性色/gray-5 */
        text-align: left;

        .screenMobile({
          font-size: 20px;
        });
      }

      /* 分割线 */
      .divider {
        width: 100%;
        height: 1px;
        background-color: #dbd7d4; /* 中性色/gray-1 */
      }

      /* 订单信息 */
      .order-info {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 30px;

        .screenMobile({
          gap: 20px;
        });
      }

      .info-row {
        width: 100%;
        height: 23px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        border-radius: 8px;
      }

      .info-label {
        font-size: 16px;
        font-weight: 400; /* Regular */
        color: #554e49; /* 中性色/gray-3 */
        text-align: left;

        .screenMobile({
          font-size: 12px;
        });
      }

      .info-value {
        font-size: 16px;
        font-weight: 400; /* Regular */
        color: #554e49; /* 中性色/gray-3 */
        text-align: left;

        .screenMobile({
          font-size: 12px;
        });
      }

      .info-value.order-number {
        color: #cc0100; /* 品牌色/brand-1 */
      }
    }
  }
}

.debit-card-modal {
  .payment-pending-container {
    background-color: #ffffff; /* 中性色/gray-0 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 30px;
    box-sizing: border-box;

    .screenMobile({
      gap: 20px;
    });
  }

  /* 支付状态区域 */
  .payment-status-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;

    .screenMobile({
      gap: 5px;
    });
  }

  .card-icon-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .card-icon {
    width: 60px;
    height: 50px;

    .screenMobile({
      width: 36px;
      height: 30px;
    });
  }

  /* 支付状态文字 */
  .payment-status-text {
    font-size: 28px;
    font-weight: 500; /* Medium */
    color: #52c41a; /* 状态色/green-2 */
    text-align: left;
    line-height: 41px;

    .screenMobile({
      font-size: 20px;
    });
  }

  /* 支付说明文字 */
  .payment-instructions {
    font-size: 16px;
    font-weight: 400; /* Regular */
    color: #101010; /* 中性色/gray-5 */
    text-align: left;
    line-height: 1.5;

    .screenMobile({
    line-height: 1.2;
    font-size: 12px;
    });
  }

  .payment-instructions p {
    color: #101010; /* 中性色/gray-5 */
  }

  .payment-instructions p:last-child {
    margin-bottom: 0;
  }

  /* 弹窗警告文字特殊颜色 */
  .payment-instructions .popup-warning {
    color: #c47330; /* 状态色/orange-3 */
  }
}

.payment-info,
.payment-result-info {
  .sz-modal-content-footer-btn {
    .screenMobile({
      flex-direction: column;
      gap: 10px;

      .confirm-btn,
      .cancel-btn {
        width: 100%;
        margin: 0;

        font-size: 16px
      }
    });
  }
}
