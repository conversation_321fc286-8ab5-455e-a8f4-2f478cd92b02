<%
    const _type = typeof type !== 'undefined' ? type : undefined; // 默认为 ： 倒计时  PAYMENT_SUCCESS ：支付成功 TICKET_FAILED ：支付成功，出票失败
%>

<div class="payment-top-tip">
  <div class="payment-top-tip-top">
    <% if( _type === 'PAYMENT_SUCCESS' ){ %>
    <img src="../../images/payment/success.svg" class="clock-icon" />
    <% } else if( _type === 'TICKET_FAILED' ){ %>
    <img src="../../images/payment/failed.svg" class="clock-icon" />
    <% } else { %>
    <img src="../../images/payment/clock.svg" class="clock-icon" />
    <% } %>

    <% if( _type === 'PAYMENT_SUCCESS' ){ %>
    <span class="main-message">Ticket Issued Successfully!</span>
    <% } else if( _type === 'TICKET_FAILED' ){ %>
    <span class="main-message-failed">
      Payment successful，
      <br />
      But ticket issuance failed.
    </span>
    <% } else { %>
    <span class="main-message">Your order has been generated!</span>
    <% } %>
  </div>

  <div class="banner-content">
    <% if( _type === 'PAYMENT_SUCCESS' ){ %>
    <div class="message-area">
      <p>
        For international flights, please arrive at the airport
        <span class="highlight-brand">120 minutes</span>
        early for check-in. Specific check-in time is subject to airport announcements.（对于国际航班，请提前 120
        分钟到达机场办理值机手续。具体值机时间以机场公告为准。）
      </p>

      <div class="order-number">
        Order Number:
        <span class="highlight-order-number">201705190909493671</span>
      </div>
      <div class="order-number">PNR: NGYBT1</div>
    </div>
    <% } else if( _type === 'TICKET_FAILED' ){ %>
    <div class="message-area">
      <p>
        The airline's system is under temporary maintenance, the server has crashed, or there is data abnormality,
        making it impossible to complete the ticket issuance operation.*
        <span class="highlight-brand">
          （航空公司系统正在临时维护、服务器已崩溃或存在数据异常，导致无法完成出票操作）
        </span>
      </p>

      <div class="order-number">
        Order Number:
        <span class="highlight-order-number">201705190909493671</span>
      </div>
      <div class="order-number">PNR: NGYBT1</div>
    </div>
    <% } else { %>
    <div class="message-area">
      <p>
        Please complete the payment within
        <span class="highlight-brand countdown-timer" id="countdownTimer">15:00</span>
        minutes, or the order will be canceled automatically. (请在
        <span class="countdown-timer-cn" id="countdownTimerCn">15:00</span>
        前完成付款，否则订单将自动取消。)
      </p>

      <div class="order-number">
        Order Number:
        <span class="highlight-order-number">201705190909493671</span>
      </div>
    </div>
    <% } %>
  </div>

  <!-- 倒计时结束提示 -->
  <!-- <div class="timeout-message" id="timeoutMessage" style="display: none">
    <div class="timeout-content">
      <img src="../../../images/payment/warning.png" class="warning-icon" />
      <p class="timeout-text">Payment time has expired, order has been canceled automatically.</p>
      <p class="timeout-text-cn">付款时间已过期，订单已自动取消。</p>
      <button class="retry-btn" onclick="location.reload()">Try Again / 重新尝试</button>
    </div>
  </div> -->
</div>
