/**
 * 支付倒计时类
 * 提供完整的倒计时功能，包括状态持久化、时间同步等
 */
class PaymentCountdown {
  constructor(duration = 15 * 60) {
    // 默认15分钟
    this.duration = duration;
    this.remaining = duration;
    this.timer = null;
    this.isExpired = false;

    this.timerElement = document.getElementById('countdownTimer');
    this.timerElementCn = document.getElementById('countdownTimerCn');
    this.timeoutMessage = document.getElementById('timeoutMessage');

    this.init();
  }

  init() {
    // 检查是否有保存的倒计时状态
    const savedState = this.loadState();
    if (savedState && savedState.remaining > 0) {
      this.remaining = savedState.remaining;
    }

    this.updateDisplay();
    this.start();

    // 页面可见性变化时的处理
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.saveState();
      } else {
        this.syncTime();
      }
    });

    // 页面卸载时保存状态
    window.addEventListener('beforeunload', () => {
      this.saveState();
    });
  }

  start() {
    if (this.timer) {
      clearInterval(this.timer);
    }

    this.timer = setInterval(() => {
      this.tick();
    }, 1000);
  }

  tick() {
    if (this.remaining <= 0) {
      this.expire();
      return;
    }

    this.remaining--;
    this.updateDisplay();
    this.saveState();

    // 最后1分钟时添加警告样式
    if (this.remaining <= 60) {
      this.timerElement.classList.add('warning');
      this.timerElementCn.classList.add('warning');
    }
  }

  updateDisplay() {
    const minutes = Math.floor(this.remaining / 60);
    const seconds = this.remaining % 60;
    const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

    if (this.timerElement) {
      this.timerElement.textContent = timeString;
    }
    if (this.timerElementCn) {
      this.timerElementCn.textContent = timeString;
    }
  }

  expire() {
    this.isExpired = true;
    clearInterval(this.timer);

    // 显示超时消息
    if (this.timeoutMessage) {
      this.timeoutMessage.style.display = 'flex';
    }

    // 清除保存的状态
    this.clearState();

    // 可以在这里添加其他超时处理逻辑
    this.onExpire();
  }

  onExpire() {
    // 超时回调，可以发送请求通知服务器订单已过期
    console.log('Payment countdown expired');

    // 示例：发送过期通知到服务器
    // fetch('/api/order/expire', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ orderNumber: '201705190909493671' })
    // });
  }

  saveState() {
    const state = {
      remaining: this.remaining,
      timestamp: Date.now(),
    };
    localStorage.setItem('paymentCountdown', JSON.stringify(state));
  }

  loadState() {
    try {
      const saved = localStorage.getItem('paymentCountdown');
      if (saved) {
        return JSON.parse(saved);
      }
    } catch (e) {
      console.error('Failed to load countdown state:', e);
    }
    return null;
  }

  clearState() {
    localStorage.removeItem('paymentCountdown');
  }

  syncTime() {
    // 同步时间，处理页面切换时的时间差
    const savedState = this.loadState();
    if (savedState) {
      const elapsed = Math.floor((Date.now() - savedState.timestamp) / 1000);
      this.remaining = Math.max(0, savedState.remaining - elapsed);
      this.updateDisplay();

      if (this.remaining <= 0) {
        this.expire();
      }
    }
  }

  // 公共方法：手动停止倒计时
  stop() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    this.clearState();
  }

  // 公共方法：重置倒计时
  reset(newDuration) {
    this.stop();
    this.duration = newDuration || this.duration;
    this.remaining = this.duration;
    this.isExpired = false;
    this.timerElement.classList.remove('warning');
    this.timerElementCn.classList.remove('warning');
    if (this.timeoutMessage) {
      this.timeoutMessage.style.display = 'none';
    }
    this.updateDisplay();
    this.start();
  }
}

// 页面加载完成后初始化倒计时
document.addEventListener('DOMContentLoaded', function () {
  // 初始化15分钟倒计时
  window.paymentCountdown = new PaymentCountdown(15 * 60);
});

// 支付成功后可以调用这个函数停止倒计时
function onPaymentSuccess() {
  if (window.paymentCountdown) {
    window.paymentCountdown.stop();
  }
  // 其他支付成功处理逻辑
}

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PaymentCountdown;
}
