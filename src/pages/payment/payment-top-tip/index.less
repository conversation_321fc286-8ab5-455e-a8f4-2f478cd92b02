@import '../../../less/variables.less';
@import '../../../less/mediaMixin.less';

.payment-top-tip {
  max-width: 944px;
  margin: 0 auto;

  .screenPad({
    padding: 0 20px;
  });

  .screenMobile({
    padding: 0;
  });

  .payment-top-tip-top {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .clock-icon {
      width: 50px;
      height: 50px;

      .screenMobile({
        width: 30px;
        height: 30px;
      });
    }

    .main-message,
    .main-message-failed {
      margin-left: 20px;

      color: @orange-3;
      font-size: 28px;
      font-weight: 500;

      .screenMobile({
        font-size: 14px;
      });
    }

    .main-message-failed {
      color: @brand-1;
    }
  }

  .banner-content {
    display: flex;
    align-items: flex-start;
    justify-content: center;

    .message-area {
      text-align: left;
      flex-grow: 1;

      p {
        margin: 0 0 15px 0;
        color: @gray-4;
        font-size: 16px;
        font-weight: 500;

        .screenMobile({
          margin: 0 0 10px 0;

          font-size: 12px;
        });
      }

      .highlight-brand {
        color: @brand-1;
        font-weight: 500;
      }

      .order-number {
        display: inline-block;

        background-color: #fcf0ce;
        padding: 5px 10px;
        border-radius: 8px;
        font-size: 16px;
        color: @sub-4;

        .screenMobile({
          font-size: 12px;
        });

        .highlight-order-number {
          color: @brand-1;
          text-decoration: underline;
        }
      }
    }
  }
}
