tippy('[data-tippy-content]');

/**
 * 附加服务页面交互逻辑
 */
class AdditionalServicesManager {
  constructor() {
    this.$serviceOptions = $('.service-option');
    this.$serviceCards = $('[id^="additional-services-"][id$="-card"]');
    this.init();
  }

  init() {
    this.bindEvents();
  }

  bindEvents() {
    this.$serviceOptions.on('click', e => this.handleServiceOptionClick(e));
  }

  handleServiceOptionClick(event) {
    const $clickedOption = $(event.currentTarget);
    const targetId = $clickedOption.attr('data-target');

    // 移除所有选项的active状态
    this.$serviceOptions.removeClass('active');

    // 隐藏所有卡片
    this.$serviceCards.removeClass('show');
    // 激活当前选项
    $clickedOption.addClass('active');

    // 显示对应的卡片
    const $targetCard = $('#' + targetId);
    if ($targetCard.length) {
      $targetCard.addClass('show');
    }
  }
}

// 页面加载完成后初始化
$(document).ready(function () {
  new AdditionalServicesManager();
});
