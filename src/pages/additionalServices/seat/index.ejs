<div>
  <div class="select-seat-container">
    <% [...new Array(3)].forEach((_, index)=>{ %>
    <div class="seat-section">
      <div class="wrap-expand-card"><%- include('../expand-card/index', { contentId: 'seat-content-' + index }) %></div>
      <div id="seat-content-<%= index %>" style="display: none"><%- include('./content') %></div>
    </div>
    <% }) %>
  </div>

  <div class="boarding-services">
    <div class="boarding-services-title">Boarding services:</div>
    <div class="boarding-services-text">
      Gentle hint:Select your seat and confirm. The system will automatically reserve check-in services for you after
      the ticket is issued. The check-in results will be sent to you via SMS and email after flight initialization.
    </div>
  </div>

  <!-- Button container with Skip and Confirm buttons -->
  <div class="button-container">
    <button class="btn-skip" type="button">Skip this step</button>
    <button class="btn-confirm" type="button">Confirm</button>
  </div>
</div>
