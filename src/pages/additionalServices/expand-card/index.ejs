<%
  const _contentId = typeof contentId !== 'undefined'? contentId : '';

%>

<div class="expand-card">
  <div class="flight-info-card">
    <div class="card-header"></div>
    <div class="card-content">
      <div class="flight-details">
        <div class="journey-badge">Journey 2</div>
        <div class="route-info">
          <span class="departure-city">Beijing</span>
          <div class="route-arrow">
            <img src="../../images/additionalServices/way.svg" alt="" />
          </div>
          <span class="arrival-city">Guangzhou</span>
        </div>
        <div class="flight-date">2025-06-06</div>
        <div class="changes-info">
          <span class="changes-text">Changes/Cancellations</span>

          <div
            class="tips"
            role="tooltip"
            tabindex="0"
            data-tooltip="说明"
            data-tippy-content="type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type"
            data-tippy-allowHTML="true">
            <div class="info-icon">
              <img src="../../images/additionalServices/question.svg" alt="" />
            </div>
          </div>
        </div>
        <div class="status-info">
          <div class="status-icon">
            <img src="../../images/additionalServices/check.svg" alt="" />
          </div>
          <span class="status-text">Selected</span>
        </div>
      </div>
      <div class="expand-control">
        <span class="expand-text">expand</span>
        <div class="expand-icon">
          <img src="../../images/additionalServices/arrow.svg" alt="" />
        </div>
      </div>
    </div>

    <div class="expand-content" style="display: none" data-source="<%= _contentId %>"></div>
  </div>
</div>
