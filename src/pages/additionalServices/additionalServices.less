@import '../../less/variables.less';
@import '../../less/mediaMixin.less';

@import './expand-card/index.less';
@import './seat/index.less';

.as {
  .wrap-payment-top-tip {
    background: #fdf7e8;
    border: 1px solid #eeb71c;
    border-radius: 8px;
    padding: 20px;
    margin: 30px 0;

    .payment-top-tip {
      max-width: none;

      .clock-icon {
        width: 30px;
        height: 30px;
      }

      .main-message {
        margin-left: 8px;

        font-size: 24px;
      }
    }
  }
}

.additional-services-container {
  .additional-services-tag {
    display: flex;
    flex-direction: row;
    gap: 20px;
    margin: 20px 0;

    .service-option {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 10px 20px;
      border-radius: 8px;
      border: 1px solid #ccc;
      opacity: 0.8;
      background: #fff;
      position: relative;
      cursor: pointer;

      &.active {
        opacity: 1;
        border: 2px solid #cc0100;
        background: linear-gradient(180deg, #cc0100 0%, rgba(204, 1, 0, 0.5) 100%);
        background-color: #cc0100;

        .service-content {
          .service-text {
            color: #fff;
          }

          .seat-icon {
            background-image: url('../../images/additionalServices/seat.svg');
          }

          .baggage-icon {
            background-image: url('../../images/additionalServices/baggage.svg');
          }

          .insurance-icon {
            background-image: url('../../images/additionalServices/insurance.svg');
          }
        }
      }

      .service-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 8px;
        padding: 30px 37px;

        .icon-container {
          width: 50px;
          height: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .seat-icon {
          width: 100%;
          height: 100%;
          background-image: url('../../images/additionalServices/seat-1.svg');
        }

        .baggage-icon {
          width: 100%;
          height: 100%;
          background-image: url('../../images/additionalServices/baggage-1.svg');
        }

        .insurance-icon {
          width: 100%;
          height: 100%;
          background-image: url('../../images/additionalServices/insurance-1.svg');
        }

        .service-text {
          font-size: 20px;
          font-weight: 500;
          color: #2c2420;
          text-align: left;
        }
      }

      .service-status {
        position: absolute;
        top: 10px;
        right: 20px;

        .status-content {
          display: flex;
          align-items: center;
          gap: 4px;

          .check-icon {
            width: 16px;
            height: 16px;
          }

          .status-text {
            font-size: 14px;
            font-weight: 500;
            color: #46a716;
          }
        }
      }
    }
  }

  .additional-services-seat-card,
  .additional-services-baggage-card,
  .additional-services-insurance-card {
    display: none;
    margin-top: 20px;

    &.show {
      display: block;
    }
  }

  .additional-services-seat-card.show {
    display: block;
  }
}
