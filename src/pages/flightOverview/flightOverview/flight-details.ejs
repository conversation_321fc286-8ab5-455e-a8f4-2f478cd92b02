<% 
  const _isStop = typeof isStop !== 'undefined' ? isStop : undefined;
%>

<div class="flight-details">
  <div class="time-details">
    <div class="departure">
      <div class="time">10:30</div>
      <div class="date">2025-07-01</div>
    </div>

    <div class="duration">
      <div class="divider"></div>
      <div class="duration-info">
        <span class="duration-value">3h 0min</span>
        <% if(_isStop){ %>
        <span>Stopping in Shanghai</span>
        <span>Stopping in guangzhou</span>
        <% } %>
      </div>
      <div class="divider"></div>
    </div>

    <div class="arrival">
      <div class="date">2025-07-01</div>
      <div class="time">13:30</div>
    </div>
  </div>

  <div class="airport-details">
    <div class="departure-airport">shenzhen international airport T1</div>
    <div class="arrival-airport">BeiJing Capital Airport T1</div>
  </div>

  <div class="flight-meal-info">
    <div class="flight-info">
      <div class="flight-number">
        <img src="../../images/flightOptions/airline-logo.svg" />
        ZH8034
      </div>
      <div class="aircraft-type">Boeing 738</div>
      <div class="shared-flight">共享</div>
    </div>

    <div class="meal-info">
      <div
        class="flight-details-link"
        role="button"
        tabindex="0"
        aria-label="Meal information"
        data-tippy-content="<div class='flight-details-tippy'>
          <div class='meal'>
            <div class='cabin-class'>
              <div class='flight-info-table' role='table' aria-label='Flight details information'>
                <div class='table-column' role='columngroup'>
                  <div class='table-header' role='columnheader'>
                    <span>Flight number</span>
                  </div>
                  <div class='table-body' role='rowgroup'>
                    <div class='table-cell' role='cell'>
                      <span>ZH307</span>
                    </div>
                    <div class='table-cell' role='cell'>
                      <span>ZH8034</span>
                    </div>
                  </div>
                </div>

                <div class='table-column' role='columngroup'>
                  <div class='table-header' role='columnheader'>
                    <span>Leg</span>
                  </div>
                  <div class='table-body' role='rowgroup'>
                    <div class='table-cell' role='cell'>
                      <span>Shenzhen-Shanghai</span>
                    </div>
                    <div class='table-cell' role='cell'>
                      <span>Shanghai-Beijin</span>
                    </div>
                  </div>
                </div>

                <div class='table-column' role='columngroup'>
                  <div class='table-header' role='columnheader'>
                    <span>Meals</span>
                  </div>
                  <div class='table-body' role='rowgroup'>
                    <div class='table-cell' role='cell'>
                      <span>Dinner</span>
                    </div>
                    <div class='table-cell' role='cell'>
                      <span>Dim sum</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class='meal-title'>Tips：</div>
              <ol class='meal-ol'>
                <li>1. The specific types of meals are subject to the actual equipment of the flight.</li>
                <li>2. Meals for codeshare flights shall be based on the actual operating flight;</li>
                <li>3. In case of special circumstances such as flight delays, changes in aircraft types, or sudden public health emergencies that may result in adjustments to the meal plan, please refer to the actual configuration of the flight;</li>
              </ol>
            </div>
          </div>
        </div>"
        data-tippy-allowHTML="true">
        <img class="icon" src="../../images/flightOptions/meals.svg" alt="Meal icon" />
        <span>Meal</span>
      </div>

      <div>Business class/J class</div>
    </div>
  </div>

  <div class="carrier-info">Actual Carrier: Shenzhen Airlines</div>
</div>
