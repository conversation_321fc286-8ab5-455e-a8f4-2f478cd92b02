<% 
  const _isTransfer = typeof isTransfer !== 'undefined' ? isTransfer : undefined;
  const _isStop = typeof isStop !== 'undefined' ? isStop : undefined;
%>

<!-- Header section -->
<div class="segment-header">
  <div class="segment-type">One Way</div>
  <div class="segment-class">Business Premium</div>
</div>

<!-- Flight details -->
<%- include('./flight-details', {isStop: _isStop} ) %>

<% if(_isTransfer){ %>

<!-- Transfer information bar -->
<div class="transfer-info-bar">
  <div class="transfer-line left"></div>
  <div class="transfer-info">
    <span class="transfer-text">Transfer to Shanghai 1h30m</span>
  </div>
  <div class="transfer-line right"></div>
</div>

<!-- Flight details -->
<%- include('./flight-details', {isStop: _isStop} ) %>

<% } %>

<!-- Notes section -->
<div class="notes-section">
  <div class="notes-header">Notes:</div>
  <div class="notes-content">
    1. The time of departure and arrival both refers to local time. Since there is likely a time difference between the
    place of departure and the destination, please properly arrange your schedule. 2. We will reserve the seat(s) for
    you after the order is paid. Please make the payment as soon as the order is generated.
  </div>
</div>

<div class="policy-header">Flight Rights:</div>
<!-- Information cards -->
<%- include('./info-cards') %>

<!-- Cancellation and changes -->
<div class="policy-header">Changes/Cancellations:</div>

<div class="policy-tables">
  <div class="policy-table">
    <div class="table-header">Ticket Refund</div>
    <div class="table-row header-row">
      <div class="condition">Before the flight takes off</div>
    </div>
    <div class="table-row">
      <div class="condition">All unused</div>
      <div class="value">CNY600.00</div>
    </div>
    <div class="table-row">
      <div class="condition">partial use</div>
      <div class="value">CNY400.00</div>
    </div>
    <div class="table-row header-row">
      <div class="condition">After the flight takes off</div>
    </div>
    <div class="table-row">
      <div class="condition">All unused</div>
      <div class="value">CNY500.00</div>
    </div>
    <div class="table-row">
      <div class="condition">partial use</div>
      <div class="value">CNY300.00</div>
    </div>
  </div>

  <div class="policy-table">
    <div class="table-header">Changes</div>
    <div class="table-row header-row">
      <div class="condition">Before the flight takes off</div>
    </div>
    <div class="table-row">
      <div class="condition">All unused</div>
      <div class="value">free</div>
    </div>
    <div class="table-row">
      <div class="condition">partial use</div>
      <div class="value">free</div>
    </div>
    <div class="table-row header-row">
      <div class="condition">After the flight takes off</div>
    </div>
    <div class="table-row">
      <div class="condition">All unused</div>
      <div class="value">free</div>
    </div>
    <div class="table-row">
      <div class="condition">partial use</div>
      <div class="value">free</div>
    </div>
  </div>
</div>

<!-- Footer notes -->
<div class="footer-notes">
  Where the refund and rebook rules are inconsistent in several flight segments, the strictest standard among them takes
  the prevail. Where there is a price difference between seats in the same class, seat changing shall make up the
  difference; where rescheduling and upgrade fees co-occur, both shall be charged together. A passenger must make a
  cancellation request within one year from the date of commencement of first travel (or from the date of ticket
  issuance with the first segment of voyage unused). Should this time period be exceeded, the request will not be
  processed. If ticket refund is not permitted, fuel surcharge and other fees will not be refunded.
</div>
