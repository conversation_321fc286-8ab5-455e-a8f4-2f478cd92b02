/**
 * 价格日历组件样式
 * 基于设计稿的颜色系统和布局
 */

@import '../../less/variables.less';
@import '../../less/mediaMixin.less';

.price-calendar-container {
  width: 703px;
  background: @gray-0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .screenMobile({
    width: 375px;
  });

  // 头部样式
  .price-calendar-header {
    height: 60px;
    background: @gray-1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    border-radius: 8px 8px 0 0;
    position: relative;

    .lowest-price-indicator {
      display: flex;
      align-items: center;
      gap: 4px;

      // 头部点
      .price-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: @brand-1;
      }

      .price-text {
        font-size: 28px;
        color: @gray-5;
        font-weight: normal;

        .screenMobile({
          font-size: 20px;
        });
      }
    }

    .__text-button {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: 20px;

      .close-icon {
        width: 16px;
        height: 16px;
      }
    }
  }

  // 日历主体
  .price-calendar-body {
    padding: 20px;

    .screenMobile({
      padding: 20px 10px;
    });

    .month-navigation {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      margin-bottom: 20px;

      .nav-button {
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: opacity 0.2s;

        &:hover {
          opacity: 0.8;
        }

        .nav-text {
          font-size: 16px;
          color: @sub-4;
          margin: 0 8px;

          .screenMobile({
            font-size: 14px;
            margin: 0;
          });
        }

        .nav-arrow {
          width: 30px;
          height: 30px;
          background: @gray-1;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          &:hover {
            background: darken(@gray-1, 5%);
          }

          svg {
            width: 15px;
            height: 16px;
          }

          &.next svg {
            transform: rotate(180deg);
          }
        }

        &.prev-month {
          .nav-arrow {
            order: -1;
            margin-right: 8px;
          }
        }

        &.next-month {
          .nav-arrow {
            margin-left: 8px;
          }
        }
      }

      .current-month {
        .month-name {
          font-size: 16px;
          font-weight: 500;
          color: @gray-5;
        }
      }
    }

    // 星期标题
    .weekdays-header {
      display: flex;
      margin-bottom: 0;

      .weekday-cell {
        flex: 1;
        height: 32px;
        background: @sub-1;
        border: 1px solid transparent;
        display: flex;
        align-items: center;
        justify-content: center;

        .weekday-text {
          font-size: 14px;
          font-weight: 500;
          color: @gray-4;
        }

        &.weekend .weekday-text {
          font-weight: 500;
          color: @gray-4;
        }
      }
    }

    // 日历内容容器（包含背景图片）
    .calendar-content {
      position: relative;
      border-radius: 8px;
      overflow: hidden;

      // 确保背景图片不会影响内容的可读性
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        // background: rgba(255, 255, 255, 0.5);
        z-index: 1;
        pointer-events: none;
      }
    }

    // 日历网格
    .calendar-grid {
      position: relative;
      z-index: 2;
      .calendar-week {
        display: flex;

        .calendar-cell {
          flex: 1;
          height: 70px;
          // border: 1px solid transparent;
          cursor: pointer;
          transition: all 0.2s;
          display: flex;
          align-items: start;
          justify-content: center;
          padding-top: 6px;

          .screenMobile({
            height: 56px;
          });

          .date-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1px;
            width: 76px;

            .screenMobile({
              width: 46px;
            });

            .day-number {
              font-size: 16px;
              color: @gray-3;

              .screenMobile({
                font-size: 14px;
              });
            }

            .price-info {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 4px;

              .screenMobile({
                gap: 2px;
              });

              .price-text {
                font-size: 14px;
                color: @gray-4;
                line-height: 20px;
              }

              // 最低价格点
              .price-dot {
                &.lowest {
                  width: 8px;
                  height: 8px;
                  border-radius: 50%;

                  background: @brand-1;
                }
              }
            }
          }

          // 其他月份的日期
          &.other-month {
            .day-number {
              color: @gray-1;
            }
          }

          // 今天
          &.today {
            .day-number {
              color: @brand-1;
              font-weight: 500;
            }
          }

          // 选中状态
          &.selected {
            background: @brand-1;
            border-radius: 8px;

            .day-number {
              color: @gray-0;
              font-weight: 500;
            }

            .price-info .price-text {
              color: @gray-0;
            }

            // 选中状态 最低价格点
            .price-info .price-dot.lowest {
              background: @gray-0;
            }
          }

          // 周末
          &.weekend {
            .day-number {
              color: @brand-1;
              font-weight: 500;
            }

            &.selected {
              .day-number {
                color: @gray-0;
                font-weight: 500;
              }
            }
          }

          // 最低价格
          &.lowest-price {
            .price-info .price-dot {
              border-color: @gray-0;
              background-color: red;
            }
          }

          // 禁用状态
          &.disabled {
            cursor: not-allowed;
            opacity: 0.5;

            .day-number {
              color: @gray-1;
            }
          }

          // 悬停效果
          &:not(.disabled):hover {
            background: @sub-1;
            border-radius: 8px;
          }

          // 焦点状态
          &:focus {
            outline: 2px solid @brand-1;
            outline-offset: 2px;
          }
        }
      }
    }
  }

  // 底部
  .price-calendar-footer {
    padding: 0 20px 20px;

    .currency-info {
      font-size: 20px;
      color: @gray-5;
    }
  }
}

// 无障碍支持
.price-calendar-container {
  .calendar-cell {
    &:focus-visible {
      outline: 2px solid @brand-1;
      outline-offset: 2px;
      z-index: 1;
    }
  }
}

// 动画效果
.price-calendar-container {
  .calendar-cell {
    transition: background-color 0.2s ease, transform 0.1s ease;

    &:active {
      transform: scale(0.98);
    }
  }

  .nav-arrow {
    transition: background-color 0.2s ease, transform 0.1s ease;

    &:active {
      transform: scale(0.95);
    }
  }
}
