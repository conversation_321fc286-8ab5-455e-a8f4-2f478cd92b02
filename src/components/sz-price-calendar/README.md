# 价格日历组件 (PriceCalendar)

基于 Antd Calendar 组件交互逻辑设计的航班价格日历组件，完全按照 MasterGo 设计稿实现。

## 功能特性

- ✅ 完整的日历视图，支持月份切换
- ✅ 价格展示和最低价格标识
- ✅ 键盘导航支持 (方向键、Enter、空格)
- ✅ 响应式设计，支持移动端
- ✅ 无障碍访问支持
- ✅ 自定义样式主题
- ✅ 事件回调机制

## 设计规范

### 颜色系统 (基于设计稿 Token)

```less
@brand-color: #cc0100; // 品牌色/brand-1
@brand-purchase: #e64343; // 品牌色/brand-4(购票)
@gray-5: #101010; // 中性色/gray-5
@gray-4: #2c2420; // 中性色/gray-4
@gray-3: #7a6a5e; // 辅色/sub-3
@gray-1: #dbd7d4; // 中性色/gray-1
@gray-0: #ffffff; // 中性色/gray-0
@sub-1: #f8f1e5; // 辅色/sub-1
@sub-4: #4f3d1e; // 辅色/sub-4
```

### 尺寸规范

- 容器宽度: 703px
- 容器高度: 575px
- 日期单元格: 94.71px × 70px
- 头部高度: 60px

## 快速开始

### 1. 引入文件

```html
<!-- CSS -->
<link rel="stylesheet" href="path/to/PriceCalendar.css" />

<!-- JavaScript (需要先引入 jQuery) -->
<script src="path/to/jquery-3.7.1.min.js"></script>
<script src="path/to/PriceCalendar.js"></script>
```

### 2. HTML 结构

```html
<div id="price-calendar"></div>
```

### 3. 初始化组件

```javascript
const priceCalendar = new PriceCalendar({
  container: '#price-calendar',
  data: [
    {
      date: '2024-06-15',
      price: 1920,
      available: true,
    },
    // ... 更多数据
  ],
  currency: 'CNY',
  onDateSelect: function (dateStr, dayData) {
    console.log('选择日期:', dateStr, dayData);
  },
  onMonthChange: function (newMonth) {
    console.log('月份变化:', newMonth);
  },
});
```

## API 文档

### 构造函数选项

| 参数          | 类型     | 默认值            | 说明                      |
| ------------- | -------- | ----------------- | ------------------------- |
| container     | string   | '#price-calendar' | 容器选择器                |
| data          | Array    | []                | 价格数据数组              |
| selectedDate  | string   | null              | 初始选中日期 (YYYY-MM-DD) |
| currentMonth  | Date     | new Date()        | 初始显示月份              |
| currency      | string   | 'CNY'             | 货币单位                  |
| onDateSelect  | Function | () => {}          | 日期选择回调              |
| onMonthChange | Function | () => {}          | 月份变化回调              |

### 数据格式

```javascript
const data = [
  {
    date: '2024-06-15', // 日期 (YYYY-MM-DD)
    price: 1920, // 价格 (数字)
    available: true, // 是否可用 (可选，默认 true)
  },
];
```

### 公共方法

| 方法                     | 参数   | 返回值 | 说明         |
| ------------------------ | ------ | ------ | ------------ |
| updateData(newData)      | Array  | -      | 更新价格数据 |
| getSelectedDate()        | -      | string | 获取选中日期 |
| setSelectedDate(dateStr) | string | -      | 设置选中日期 |
| getCurrentMonth()        | -      | Date   | 获取当前月份 |
| setCurrentMonth(date)    | Date   | -      | 设置当前月份 |
| previousMonth()          | -      | -      | 切换到上个月 |
| nextMonth()              | -      | -      | 切换到下个月 |

### 事件回调

#### onDateSelect(dateStr, dayData)

日期选择时触发

- `dateStr`: 选中的日期字符串 (YYYY-MM-DD)
- `dayData`: 该日期的数据对象，如果没有数据则为 undefined

#### onMonthChange(newMonth)

月份变化时触发

- `newMonth`: 新的月份 Date 对象

## 键盘操作

| 按键              | 功能               |
| ----------------- | ------------------ |
| 方向键            | 在日期间导航       |
| Enter / 空格      | 选择当前聚焦的日期 |
| Ctrl/Cmd + 左箭头 | 上个月             |
| Ctrl/Cmd + 右箭头 | 下个月             |

## 样式定制

### CSS 类名

| 类名                        | 说明       |
| --------------------------- | ---------- |
| .price-calendar-container   | 主容器     |
| .price-calendar-header      | 头部区域   |
| .price-calendar-body        | 主体区域   |
| .calendar-cell              | 日期单元格 |
| .calendar-cell.today        | 今天       |
| .calendar-cell.selected     | 选中状态   |
| .calendar-cell.weekend      | 周末       |
| .calendar-cell.lowest-price | 最低价格   |
| .calendar-cell.disabled     | 禁用状态   |
| .calendar-cell.other-month  | 其他月份   |

### 自定义主题

可以通过覆盖 CSS 变量来自定义主题：

```css
.price-calendar-container {
  --brand-color: #your-brand-color;
  --gray-0: #your-background-color;
  /* ... 其他颜色变量 */
}
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- IE 11+ (需要 polyfill)

## 依赖

- jQuery 3.7.1+

## 示例

查看 `demo.html` 文件获取完整的使用示例。

## 更新日志

### v1.0.0

- 初始版本发布
- 基础日历功能
- 价格展示
- 键盘导航
- 响应式设计

## 许可证

MIT License
