/**
 * 价格日历组件
 * 参考 Antd Calendar 组件交互逻辑，结合航班价格展示
 */

class PriceCalendar {
  constructor(options = {}) {
    this.container = options.container || '#price-calendar';
    this.data = options.data || [];
    this.selectedDate = options.selectedDate || null;
    this.currentMonth = options.currentMonth || new Date();
    this.currency = options.currency || 'CNY';
    this.minDate = options.minDate ? this._parseDate(options.minDate) : null;
    this.maxDate = options.maxDate ? this._parseDate(options.maxDate) : null;
    this.onDateSelect = options.onDateSelect || (() => {});
    this.onMonthChange = options.onMonthChange || (() => {});

    this.init();
  }

  /**
   * 安全地解析日期字符串，避免时区问题
   * @param {string} dateStr 日期字符串 (YYYY-MM-DD)
   * @returns {Date} 本地时区的日期对象
   */
  _parseDate(dateStr) {
    if (!dateStr) return null;

    // 如果已经是Date对象，直接返回
    if (dateStr instanceof Date) return dateStr;

    // 解析 YYYY-MM-DD 格式的日期字符串
    const parts = dateStr.split('-');
    if (parts.length === 3) {
      const year = parseInt(parts[0], 10);
      const month = parseInt(parts[1], 10) - 1; // 月份从0开始
      const day = parseInt(parts[2], 10);
      return new Date(year, month, day);
    }

    // 如果格式不对，回退到原来的方法
    return new Date(dateStr);
  }

  init() {
    this.render();
    this.bindEvents();
  }

  render() {
    const container = $(this.container);
    if (!container.length) {
      console.error('Price calendar container not found');
      return;
    }

    const calendarHtml = this.generateCalendarHtml();
    container.html(calendarHtml);
  }

  generateCalendarHtml() {
    const monthData = this.getMonthData();
    const monthName = this.getMonthName(this.currentMonth);
    const canGoPrev = this.canNavigateToPreviousMonth();
    const canGoNext = this.canNavigateToNextMonth();
    const monthBgImage = this.getMonthBackgroundImage(this.currentMonth); // 月份背景图片路径

    return `
      <div class="price-calendar-container">
        <!-- 头部 -->
        <div class="price-calendar-header">
          <div class="lowest-price-indicator">
            <div class="price-dot"></div>
            <span class="price-text">Lowest price</span>
          </div>
          
          <div class="__text-button">
            <img
              src="../../images/icon-close.webp"
              class="close-icon"
              alt=""
              srcset=""
            />
          </div>
        </div>

        <!-- 日历主体 -->
        <div class="price-calendar-body">
          <div class="month-navigation">
            <div class="nav-button prev-month ${!canGoPrev ? 'disabled' : ''}">
              <span class="nav-text">Last month</span>
              <div class="nav-arrow prev">
                <svg width="15" height="16" viewBox="0 0 15 16" fill="none">
                  <path d="M3.58875 6.97146L15 6.97146L15 9.02854L3.58875 9.02854L8.6175 14.5456L7.29187 16L0 8L7.29187 0L8.6175 1.45436L3.58875 6.97146Z" fill="white"/>
                </svg>
              </div>
            </div>
            
            <div class="current-month">
              <span class="month-name">${monthName}</span>
            </div>
            
            <div class="nav-button next-month ${!canGoNext ? 'disabled' : ''}">
              <span class="nav-text">Next month</span>
              <div class="nav-arrow next">
                <svg width="15" height="16" viewBox="0 0 15 16" fill="none">
                  <path d="M3.58875 6.97146L15 6.97146L15 9.02854L3.58875 9.02854L8.6175 14.5456L7.29187 16L0 8L7.29187 0L8.6175 1.45436L3.58875 6.97146Z" fill="white"/>
                </svg>
              </div>
            </div>
          </div>
          
          <!-- 星期标题 -->
          <div class="weekdays-header">
            ${this.generateWeekdaysHtml()}
          </div>
          
          <!-- 日期网格容器，带月份背景图 -->
          <div class="calendar-content" style="background-image: url('${monthBgImage}'); background-size: auto; background-position: center; background-repeat: no-repeat;">
            <div class="calendar-grid">
              ${this.generateCalendarGrid(monthData)}
            </div>
          </div>
        </div>

        <!-- 底部货币信息 -->
        <div class="price-calendar-footer">
          <span class="currency-info">币种：${this.currency}</span>
        </div>
      </div>
    `;
  }

  generateWeekdaysHtml() {
    const weekdays = ['Sat', 'Mon', 'Tue', 'Web', 'Thu', 'Fri', 'Sun'];
    return weekdays
      .map((day, index) => {
        const isWeekend = index === 0 || index === 6; // Saturday and Sunday
        return `
        <div class="weekday-cell ${isWeekend ? 'weekend' : ''}">
          <span class="weekday-text">${day}</span>
        </div>
      `;
      })
      .join('');
  }

  generateCalendarGrid(monthData) {
    const firstDay = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth(), 1);
    const startDate = new Date(firstDay);

    // 调整到周六开始 (0=Sunday, 6=Saturday)
    const dayOfWeek = firstDay.getDay();
    const daysToSubtract = dayOfWeek === 6 ? 0 : dayOfWeek + 1;
    startDate.setDate(startDate.getDate() - daysToSubtract);

    const cells = [];
    const today = new Date();
    const lowestPrice = this.getLowestPrice(monthData);

    // 生成6周的日期
    for (let week = 0; week < 6; week++) {
      const weekRow = [];
      for (let day = 0; day < 7; day++) {
        const currentDate = new Date(startDate);
        currentDate.setDate(startDate.getDate() + week * 7 + day);

        const dateStr = this.formatDate(currentDate);
        const dayData = monthData.find(item => item.date === dateStr);
        const isCurrentMonth = currentDate.getMonth() === this.currentMonth.getMonth();
        const isToday = this.isSameDate(currentDate, today);
        const isSelected = this.selectedDate && this.isSameDate(currentDate, this._parseDate(this.selectedDate));
        const isWeekend = day === 0 || day === 6;
        const isLowestPrice = dayData && dayData.price === lowestPrice;

        weekRow.push(
          this.generateDateCell({
            date: currentDate,
            dayData,
            isCurrentMonth,
            isToday,
            isSelected,
            isWeekend,
            isLowestPrice,
          })
        );
      }
      cells.push(`<div class="calendar-week">${weekRow.join('')}</div>`);
    }

    return cells.join('');
  }

  generateDateCell({ date, dayData, isCurrentMonth, isToday, isSelected, isWeekend, isLowestPrice }) {
    const dayNumber = date.getDate();
    const price = dayData ? dayData.price : null;
    const available = dayData ? dayData.available !== false : true;
    const isOutOfRange = this.isDateOutOfRange(date);

    let cellClass = 'calendar-cell';
    if (!isCurrentMonth) cellClass += ' other-month';
    if (isToday) cellClass += ' today';
    if (isSelected) cellClass += ' selected';
    if (isWeekend) cellClass += ' weekend';
    if (isLowestPrice) cellClass += ' lowest-price';
    if (!available || isOutOfRange) cellClass += ' disabled';

    const priceHtml = price
      ? `
      <div class="price-info">
        <span class="price-text">${this.formatPrice(price)}</span>
        <div class="price-dot ${isLowestPrice ? 'lowest' : ''}"></div>
      </div>
    `
      : '';

    return `
      <div class="${cellClass}" data-date="${this.formatDate(date)}">
        <div class="date-content">
          <span class="day-number">${dayNumber}</span>
          ${priceHtml}
        </div>
      </div>
    `;
  }

  bindEvents() {
    const container = $(this.container);

    // 日期选择
    container.on('click', '.calendar-cell:not(.disabled)', e => {
      const cell = $(e.currentTarget);
      const dateStr = cell.data('date');

      if (dateStr) {
        this.selectDate(dateStr);
      }
    });

    // 月份导航
    container.on('click', '.prev-month:not(.disabled)', () => {
      this.previousMonth();
    });

    container.on('click', '.next-month:not(.disabled)', () => {
      this.nextMonth();
    });

    // 键盘导航
    container.on('keydown', '.calendar-cell', e => {
      this.handleKeyNavigation(e);
    });
  }

  selectDate(dateStr) {
    const date = this._parseDate(dateStr);

    // 检查日期是否在允许范围内
    if (this.isDateOutOfRange(date)) {
      console.warn('Selected date is out of allowed range:', dateStr);
      return;
    }

    this.selectedDate = dateStr;

    // 更新选中状态
    $(this.container).find('.calendar-cell').removeClass('selected');
    $(this.container).find(`[data-date="${dateStr}"]`).addClass('selected');

    // 触发回调
    this.onDateSelect(dateStr, this.getDateData(dateStr));
  }

  previousMonth() {
    const newMonth = new Date(this.currentMonth);
    newMonth.setMonth(newMonth.getMonth() - 1);

    // 检查是否可以切换到上个月
    if (this.canNavigateToMonth(newMonth)) {
      this.changeMonth(newMonth);
    }
  }

  nextMonth() {
    const newMonth = new Date(this.currentMonth);
    newMonth.setMonth(newMonth.getMonth() + 1);

    // 检查是否可以切换到下个月
    if (this.canNavigateToMonth(newMonth)) {
      this.changeMonth(newMonth);
    }
  }

  changeMonth(newMonth) {
    this.currentMonth = newMonth;
    this.render();
    this.onMonthChange(newMonth);
  }

  handleKeyNavigation(e) {
    const currentCell = $(e.currentTarget);
    const currentDate = currentCell.data('date');

    if (!currentDate) return;

    let targetDate = new Date(currentDate);

    switch (e.key) {
      case 'ArrowLeft':
        targetDate.setDate(targetDate.getDate() - 1);
        break;
      case 'ArrowRight':
        targetDate.setDate(targetDate.getDate() + 1);
        break;
      case 'ArrowUp':
        targetDate.setDate(targetDate.getDate() - 7);
        break;
      case 'ArrowDown':
        targetDate.setDate(targetDate.getDate() + 7);
        break;
      case 'Enter':
      case ' ':
        this.selectDate(currentDate);
        e.preventDefault();
        return;
      default:
        return;
    }

    e.preventDefault();

    // 检查目标日期是否在允许范围内
    if (this.isDateOutOfRange(targetDate)) {
      return;
    }

    // 如果目标日期不在当前月，切换月份
    if (targetDate.getMonth() !== this.currentMonth.getMonth()) {
      const targetMonth = new Date(targetDate.getFullYear(), targetDate.getMonth(), 1);
      if (this.canNavigateToMonth(targetMonth)) {
        this.changeMonth(targetMonth);
      } else {
        return;
      }
    }

    // 聚焦到目标日期
    setTimeout(() => {
      const targetCell = $(this.container).find(`[data-date="${this.formatDate(targetDate)}"]`);
      if (targetCell.length) {
        targetCell.focus();
      }
    }, 100);
  }

  // 工具方法
  getMonthData() {
    const year = this.currentMonth.getFullYear();
    const month = this.currentMonth.getMonth();

    return this.data.filter(item => {
      const itemDate = new Date(item.date);
      return itemDate.getFullYear() === year && itemDate.getMonth() === month;
    });
  }

  getLowestPrice(monthData) {
    if (!monthData.length) return null;
    return Math.min(...monthData.map(item => item.price));
  }

  getDateData(dateStr) {
    return this.data.find(item => item.date === dateStr);
  }

  getMonthName(date) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return months[date.getMonth()];
  }

  /**
   * 获取月份背景图片路径
   * @param {Date} date 日期对象
   * @returns {string} 图片路径
   */
  getMonthBackgroundImage(date) {
    const monthNumber = date.getMonth() + 1; // 获取月份数字 (1-12)
    return `../../images/temp/${monthNumber}.png`;
  }

  formatDate(date) {
    // 使用本地时区格式化日期，避免时区转换问题
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  formatPrice(price) {
    return price.toLocaleString();
  }

  isSameDate(date1, date2) {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  }

  // 公共API
  updateData(newData) {
    this.data = newData;
    this.render();
  }

  getSelectedDate() {
    return this.selectedDate;
  }

  setSelectedDate(dateStr) {
    this.selectDate(dateStr);
  }

  getCurrentMonth() {
    return this.currentMonth;
  }

  setCurrentMonth(date) {
    if (this.canNavigateToMonth(date)) {
      this.changeMonth(date);
    } else {
      console.warn('Cannot navigate to month outside allowed date range');
    }
  }

  /**
   * 检查日期是否超出允许范围
   * @param {Date} date 要检查的日期
   * @returns {boolean} 如果超出范围返回true
   */
  isDateOutOfRange(date) {
    // 创建只包含年月日的日期对象，避免时间部分的影响
    const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());

    if (this.minDate) {
      const minDateOnly = new Date(this.minDate.getFullYear(), this.minDate.getMonth(), this.minDate.getDate());
      // 如果日期小于最小日期，则超出范围
      if (dateOnly.getTime() < minDateOnly.getTime()) {
        return true;
      }
    }

    if (this.maxDate) {
      const maxDateOnly = new Date(this.maxDate.getFullYear(), this.maxDate.getMonth(), this.maxDate.getDate());
      // 如果日期大于最大日期，则超出范围
      if (dateOnly.getTime() > maxDateOnly.getTime()) {
        return true;
      }
    }

    return false;
  }

  /**
   * 检查是否可以导航到指定月份
   * @param {Date} month 目标月份
   * @returns {boolean} 如果可以导航返回true
   */
  canNavigateToMonth(month) {
    const firstDayOfMonth = new Date(month.getFullYear(), month.getMonth(), 1);
    const lastDayOfMonth = new Date(month.getFullYear(), month.getMonth() + 1, 0);

    // 如果整个月都在允许范围外，则不能导航
    if (this.minDate) {
      const minDateOnly = new Date(this.minDate.getFullYear(), this.minDate.getMonth(), this.minDate.getDate());
      // 如果该月的最后一天还小于最小日期，则不能导航
      if (lastDayOfMonth.getTime() < minDateOnly.getTime()) {
        return false;
      }
    }

    if (this.maxDate) {
      const maxDateOnly = new Date(this.maxDate.getFullYear(), this.maxDate.getMonth(), this.maxDate.getDate());
      // 如果该月的第一天还大于最大日期，则不能导航
      if (firstDayOfMonth.getTime() > maxDateOnly.getTime()) {
        return false;
      }
    }

    return true;
  }

  /**
   * 检查是否可以导航到上个月
   * @returns {boolean}
   */
  canNavigateToPreviousMonth() {
    const prevMonth = new Date(this.currentMonth);
    prevMonth.setMonth(prevMonth.getMonth() - 1);
    return this.canNavigateToMonth(prevMonth);
  }

  /**
   * 检查是否可以导航到下个月
   * @returns {boolean}
   */
  canNavigateToNextMonth() {
    const nextMonth = new Date(this.currentMonth);
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    return this.canNavigateToMonth(nextMonth);
  }

  /**
   * 设置最小日期
   * @param {string|Date} minDate 最小日期
   */
  setMinDate(minDate) {
    this.minDate = minDate ? this._parseDate(minDate) : null;
    this.render();
  }

  /**
   * 设置最大日期
   * @param {string|Date} maxDate 最大日期
   */
  setMaxDate(maxDate) {
    this.maxDate = maxDate ? this._parseDate(maxDate) : null;
    this.render();
  }

  /**
   * 设置日期范围
   * @param {string|Date} minDate 最小日期
   * @param {string|Date} maxDate 最大日期
   */
  setDateRange(minDate, maxDate) {
    this.minDate = minDate ? this._parseDate(minDate) : null;
    this.maxDate = maxDate ? this._parseDate(maxDate) : null;

    // 验证日期范围的有效性
    if (this.minDate && this.maxDate && this.minDate > this.maxDate) {
      console.error('minDate cannot be greater than maxDate');
      return;
    }

    // 如果当前选中的日期超出范围，清除选择
    if (this.selectedDate && this.isDateOutOfRange(this._parseDate(this.selectedDate))) {
      this.selectedDate = null;
    }

    // 如果当前月份不在允许范围内，切换到合适的月份
    if (!this.canNavigateToMonth(this.currentMonth)) {
      if (this.minDate) {
        this.currentMonth = new Date(this.minDate.getFullYear(), this.minDate.getMonth(), 1);
      } else if (this.maxDate) {
        this.currentMonth = new Date(this.maxDate.getFullYear(), this.maxDate.getMonth(), 1);
      }
    }

    this.render();
  }

  /**
   * 获取最小日期
   * @returns {Date|null}
   */
  getMinDate() {
    return this.minDate;
  }

  /**
   * 获取最大日期
   * @returns {Date|null}
   */
  getMaxDate() {
    return this.maxDate;
  }

  /**
   * 获取有效的日期范围信息
   * @returns {Object} 包含minDate和maxDate的对象
   */
  getDateRange() {
    return {
      minDate: this.minDate ? this.formatDate(this.minDate) : null,
      maxDate: this.maxDate ? this.formatDate(this.maxDate) : null,
    };
  }
}

// 导出到全局
window.PriceCalendar = PriceCalendar;
