/**
 * 价格日历组件入口文件
 *
 * 使用方法:
 * 1. 引入 CSS: <link rel="stylesheet" href="path/to/PriceCalendar.css">
 * 2. 引入 JS: <script src="path/to/index.js"></script>
 * 3. 初始化: const calendar = new PriceCalendar(options)
 */

// 检查依赖
if (typeof jQuery === 'undefined') {
  throw new Error('PriceCalendar requires jQuery');
}

// 导出组件 (如果支持模块化)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PriceCalendar;
}

// AMD 支持
if (typeof define === 'function' && define.amd) {
  define(['jquery'], function ($) {
    return PriceCalendar;
  });
}

// 全局导出
if (typeof window !== 'undefined') {
  window.PriceCalendar = PriceCalendar;
}

// 默认配置
PriceCalendar.defaults = {
  container: '#price-calendar',
  data: [],
  selectedDate: null,
  currentMonth: new Date(),
  currency: 'CNY',
  onDateSelect: function () {},
  onMonthChange: function () {},
};

// 版本信息
PriceCalendar.version = '1.0.0';

// 工具方法
PriceCalendar.utils = {
  /**
   * 格式化日期
   * @param {Date} date
   * @returns {string} YYYY-MM-DD
   */
  formatDate: function (date) {
    return date.toISOString().split('T')[0];
  },

  /**
   * 解析日期字符串
   * @param {string} dateStr YYYY-MM-DD
   * @returns {Date}
   */
  parseDate: function (dateStr) {
    return new Date(dateStr);
  },

  /**
   * 获取月份天数
   * @param {number} year
   * @param {number} month
   * @returns {number}
   */
  getDaysInMonth: function (year, month) {
    return new Date(year, month + 1, 0).getDate();
  },

  /**
   * 生成日期范围
   * @param {Date} startDate
   * @param {Date} endDate
   * @returns {Array<string>}
   */
  generateDateRange: function (startDate, endDate) {
    const dates = [];
    const current = new Date(startDate);

    while (current <= endDate) {
      dates.push(this.formatDate(current));
      current.setDate(current.getDate() + 1);
    }

    return dates;
  },

  /**
   * 验证价格数据格式
   * @param {Array} data
   * @returns {boolean}
   */
  validateData: function (data) {
    if (!Array.isArray(data)) return false;

    return data.every(item => {
      return (
        item && typeof item.date === 'string' && typeof item.price === 'number' && item.price >= 0
      );
    });
  },
};

console.log('PriceCalendar v' + PriceCalendar.version + ' loaded');
