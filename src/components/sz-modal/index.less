@import '../../less/variables.less';
@import '../../less/mediaMixin.less';

// 防止页面滚动的样式
body.modal-open {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
}

.sz-modal {
  width: 100vw;
  height: 100vh;
  background: rgba(16, 16, 16, 0.6);

  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;

  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease-in-out;

  &.show {
    opacity: 1;
    visibility: visible;
  }

  &-content {
    width: 710px;

    .screenMobile({
      width: 355px;
    });

    &-header {
      width: 100%;
      min-height: 48px;
      padding: 10px 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: @gray-1;
      position: relative;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;

      &-title {
        font-size: 28px;

        .screenMobile({
          font-size: 20px;
        });
      }

      .__text-button {
        .close-icon {
          width: 16px;
          height: 16px;
          position: absolute;
          top: 50%;
          right: 16px;
          transform: translateY(-50%);
        }
      }
    }

    &-body {
      padding: 30px 30px 0;
      background: @gray-0;

      .screenMobile({
        padding: 20px 20px 0;
      });
    }

    &-footer {
      padding: 30px 30px 20px;
      background: @gray-0;
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;

      .screenMobile({
        padding: 20px;
      });

      &-btn {
        display: flex;
        align-items: center;
        justify-content: end;

        .__secondary-button {
          width: 230px;
          height: 52px;
          margin-right: 20px;
        }

        .__main-button {
          width: 230px;
          height: 52px;
        }
      }
    }
  }
}
