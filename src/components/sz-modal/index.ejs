<%
  const _id = typeof id !== 'undefined' ? id: 'sz-modal';
  const _title = typeof title !== 'undefined' ? title: '';
  const _okText = typeof okText !== 'undefined' ? okText: 'Confirm';
  const _cancelText = typeof cancelText !== 'undefined' ? cancelText: 'Cancel';
%>

<div
  class="sz-modal"
  id="<%= _id %>"
  role="dialog"
  aria-modal="true"
  aria-labelledby="sz-modal-content-header-title"
  aria-describedby="sz-modal-content-body">
  <div class="sz-modal-content">
    <div class="sz-modal-content-header">
      <div class="sz-modal-content-header-title" id="sz-modal-content-header-title" role="heading" aria-level="2">
        <%= _title %>
      </div>
      <button class="__text-button" data-modal-close aria-label="Close modal">
        <img src="../../images/icon-close.webp" class="close-icon" alt="Close" aria-hidden="true" />
      </button>
    </div>

    <div class="sz-modal-content-body" id="sz-modal-content-body" role="document"></div>

    <div class="sz-modal-content-footer">
      <div class="sz-modal-content-footer-btn" role="group" aria-label="Modal actions">
        <button
          class="__secondary-button cancel-btn"
          id="cancel-btn"
          data-modal-cancel
          aria-label="Cancel and close modal">
          <%= _cancelText %>
        </button>
        <button class="__main-button confirm-btn" id="confirm-btn" data-modal-confirm aria-label="Confirm action">
          <%= _okText %>
        </button>
      </div>
    </div>
  </div>
</div>
