/**
 * Modal 弹窗组件
 * 用于航班选择页面的模态对话框功能
 */

class SZModal {
  constructor({
    title = '',
    okText = '', // false 不展示
    cancelText = '', // false 不展示
    contentDom = '',
    modalDom,
    onOk = () => {},
  }) {
    this.title = title;
    this.okText = okText;
    this.cancelText = cancelText;
    this.contentDom = contentDom;
    this.modalDom = modalDom || $('#sz-modal');
    this.onOk = onOk;
    this.renderInit();
  }

  rTitle(title) {
    if (title) {
      this.modalDom.find('#sz-modal-content-header-title').text(title);
    }
  }

  rOkText(okText) {
    const confirmBtn = this.modalDom.find('.sz-modal-content-footer-btn #confirm-btn');
    if (okText === false) {
      confirmBtn.hide();
    } else if (okText) {
      confirmBtn.text(okText).show();
    }
  }

  rCancelText(cancelText) {
    const cancelBtn = this.modalDom.find('.sz-modal-content-footer-btn #cancel-btn');
    if (cancelText === false) {
      cancelBtn.hide();
    } else if (cancelText) {
      cancelBtn.text(cancelText).show();
    }
  }

  renderInit() {
    if (!this.modalDom) return;

    this.rTitle(this.title);
    this.rOkText(this.okText);
    this.rCancelText(this.cancelText);
    this.setModalContent(this.contentDom);

    // 根据 okText 和 cancelText 的值控制按钮显示
    this.toggleButtons();

    // 点击背景关闭
    this.modalDom.on('click', this.handleBackdropClick);

    // 绑定关闭按钮事件
    this.modalDom.find('[data-modal-close]').on('click', this.close);

    // 绑定取消按钮事件
    this.modalDom.find('[data-modal-cancel]').on('click', this.close);

    // 绑定确认按钮事件
    this.modalDom.find('[data-modal-confirm]').on('click', this.confirm);
  }

  /**
   * 打开 modal
   */
  open = () => {
    // console.log(this.modalDom);
    if (!this.modalDom) return;

    // 保存当前滚动位置
    this.scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    // 添加 show 类来显示 modal
    this.modalDom.addClass('show');

    // 更强力的阻止页面滚动方案
    $('body')
      .addClass('modal-open')
      .css({
        overflow: 'hidden',
        position: 'fixed',
        top: `-${this.scrollTop}px`,
        width: '100%',
      });

    // 添加键盘事件监听（ESC 键关闭）
    document.addEventListener('keydown', this.handleKeyDown);

    // 添加触摸事件监听，防止移动端滚动
    document.addEventListener('touchmove', this.preventScroll, { passive: false });
  };

  /**
   * 关闭 modal
   */
  close = () => {
    if (!this.modalDom) return;

    // 移除 show 类来隐藏 modal
    this.modalDom.removeClass('show');

    // 恢复页面滚动和位置
    $('body').removeClass('modal-open').css({
      overflow: '',
      position: '',
      top: '',
      width: '',
    });

    // 恢复滚动位置
    if (this.scrollTop !== undefined) {
      window.scrollTo(0, this.scrollTop);
      this.scrollTop = undefined;
    }

    // 移除键盘事件监听
    document.removeEventListener('keydown', this.handleKeyDown);

    // 移除触摸事件监听
    document.removeEventListener('touchmove', this.preventScroll);
  };

  /**
   * 键盘事件处理函数
   * @param {KeyboardEvent} event
   */
  handleKeyDown = event => {
    // ESC 键关闭 modal
    if (event.key === 'Escape') {
      this.close();
    }
  };

  /**
   * 点击背景关闭 modal
   * @param {MouseEvent} event
   */
  handleBackdropClick = event => {
    // 如果点击的是 modal 背景（不是内容区域），则关闭 modal
    if (this.modalDom[0] === event.target) {
      this.close();
    }
  };

  /**
   * 防止滚动事件处理函数
   * @param {TouchEvent} event
   */
  preventScroll = event => {
    // 检查触摸事件是否发生在模态框内容区域
    const modalContent = this.modalDom.find('.sz-modal-content')[0];
    if (modalContent && !modalContent.contains(event.target)) {
      // 如果触摸事件不在模态框内容区域，阻止默认行为
      event.preventDefault();
    }
  };

  /**
   * 设置 modal 内容
   * @param {string} content - HTML 内容
   */
  setModalContent = content => {
    if (!this.modalDom && content) return;

    this.modalDom.find('#sz-modal-content-body').html(content);
  };

  /**
   * 根据 okText 和 cancelText 的值控制按钮显示
   */
  toggleButtons = () => {
    if (!this.modalDom) return;

    // 控制确认按钮显示
    const confirmBtn = this.modalDom.find('.sz-modal-content-footer-btn #confirm-btn');
    if (this.okText === false) {
      confirmBtn.hide();
    } else {
      confirmBtn.show();
    }

    // 控制取消按钮显示
    const cancelBtn = this.modalDom.find('.sz-modal-content-footer-btn #cancel-btn');
    if (this.cancelText === false) {
      cancelBtn.hide();
    } else {
      cancelBtn.show();
    }
  };

  /**
   * 检查 modal 是否打开
   * @returns {boolean}
   */
  isModalOpen = () => {
    return this.modalDom && this.modalDom.classList.contains('show');
  };

  confirm = () => {
    this.onOk();

    this.close();
  };
}
