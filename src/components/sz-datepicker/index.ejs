<%
// 设置默认值
const isRequire = typeof required !== 'undefined' ? required : false;

const inputLabel = typeof label !== 'undefined' ? label : 'Default Label';
const inputValue = typeof value !== 'undefined' ? value : '';
const inputErrorMsg = isRequire ? (typeof errorMsg !== 'undefined' ? errorMsg : '请选择日期') : '';
const inputWidth = typeof width !== 'undefined' ? width : '100%';
const inputOnChange = typeof onChange !== 'undefined' ? onChange : null;
const inputValidator = typeof validator !== 'undefined' ? validator : null;
const tips = typeof tipText !== 'undefined' ? tipText : null;
const pickerName = typeof  formName !== 'undefined' ? formName : null;

// 生成唯一ID
const inputId = 'input-' + Math.random().toString(36).substr(2, 9);
const wrapperId = 'wrapper-' + Math.random().toString(36).substr(2, 9);

const contactClass = typeof className !== 'undefined' ? className : '';

// 根据是否有错误信息决定是否添加error类
const errorClass = inputErrorMsg ? ' error' : '';
const filledClass = inputValue ? ' filled' : '';
%>

<div class="input-wrap calendar-group  form-item <%= errorClass %>"  id="<%= wrapperId %>">
    <div class="input-group   input-single filled " data-width="<%= inputWidth %>">
        <div class="label-wrap">
            <label tabindex="0" for="<%= inputId %>"><%= inputLabel %></label>
            <% if(isRequire) { %>
                <span class="require" aria-required="true">*</span>
            <% } %>
            <% if(tips){ %>
                <div role="tooltip"
                     tabindex="0"
                     aria-label="提示信息"
                     class="prompt-bubble tips" data-text="<%= tips %>">
                    <span class="icon-zh icon-zh-ask"></span>
                </div>
            <% } %>
        </div>
        <input
                type="text"
                id="<%= inputId %>"
                class="text calendar-input calendar-input-disabled-before depart-input calendar-flag"
                placeholder="Please choose"
                oninput="validateField(this)"
                onblur="validateField(this)"
                data-name="<%= pickerName %>"
                data-required="<%= isRequire %>"
                aria-label="calendar，please press enter to choose date"
        />
        <i class="icon-zh icon-zh-calendar calendar"></i>

        <%
        if(isRequire){
        %>
            <div class="error-tips" aria-label="错误提示">
                <span class="icon-zh icon-zh-error"></span>
                <span class="error-content" aria-label="<%= inputErrorMsg %>"><%= inputErrorMsg %></span>
            </div>
        <% } %>
    </div>
</div>

