<%
// 设置默认值
const isRequire = typeof required !== 'undefined' ? required : false;
const selectLabel = typeof label !== 'undefined' ? label : 'Default Select';
const selectValue = typeof value !== 'undefined' ? value : '';
const selectErrorMsg = !isRequire ? '' : (typeof errorMsg !== 'undefined' ? errorMsg : '请选择');
const selectWidth = typeof width !== 'undefined' ? width : '100%';
const selectOptions = typeof options !== 'undefined' ? options : [];
const selectOnChange = typeof onChange !== 'undefined' ? onChange : null;
const formType = typeof type !== 'undefined' ? type : null;
const selectName = typeof  formName !== 'undefined' ? formName : null;


const tips = typeof tipText !== 'undefined' ? tipText : null;

// 生成唯一ID
const selectId = 'select-' + Math.random().toString(36).substr(2, 9);
const wrapperId = 'wrapper-' + Math.random().toString(36).substr(2, 9);

// 根据是否有错误信息决定是否添加error类
const errorClass = selectErrorMsg ? ' error' : '';
const filledClass = selectValue ? ' filled' : '';

const contactClass = typeof className !== 'undefined' ? className : '';
// 找到当前值对应的显示文本
let displayText = '';
if (selectValue) {
    const selectedOption = selectOptions.find(option => {
        if (typeof option === 'string') {
            return option === selectValue;
        } else {
            return option.value === selectValue;
        }
    });
    displayText = selectedOption ? (typeof selectedOption === 'string' ? selectedOption : selectedOption.label) : '';
}
%>

<div class="input-group form-item input-single<%= filledClass %><%= errorClass %> <%= contactClass %>" id="<%= wrapperId %>" data-width="<%= selectWidth %>">
    <div class="label-wrap">
        <label tabindex="0" for="<%= selectId %>"><%= selectLabel %></label>
        <% if(isRequire) { %>
            <span class="require" aria-required="true">*</span>
        <% } %>
        <% if(tips){ %>
            <div role="tooltip"
                 aria-label="提示信息"
                 tabindex="0" class="prompt-bubble tips" data-text="<%= tips %>">
                <span class="icon-zh icon-zh-ask"></span>
            </div>
        <% } %>
    </div>
    <input
            aria-label="<%= selectLabel %>"
            type="text"
            data-inputType="select"
            id="<%= selectId %>"
            class="text form-select"
            placeholder="Please Select"
            value="<%= displayText %>"
            onchange="validateField(this)"
            onblur="validateField(this)"
            data-name="<%= selectName %>"
            data-type="<%= formType %>"
            data-errorMsg="<%= selectErrorMsg %>"
            data-options="<%= JSON.stringify(selectOptions) %>"
            data-required="<%= isRequire %>"
            readonly
    />
    <i class="icon-zh icon-zh-right-type1"></i>
    <%
    if(isRequire){
    %>
        <div class="error-tips" aria-label="错误提示">
            <span class="icon-zh icon-zh-error"></span>
            <span class="error-content" aria-label="<%= selectErrorMsg %>"><%= selectErrorMsg %></span>
        </div>
    <% } %>
</div>