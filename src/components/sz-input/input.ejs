<%
// 设置默认值
const isRequire = typeof required !== 'undefined' ? required : false;
const inputLabel = typeof label !== 'undefined' ? label : 'Default Label';
const inputValue = typeof value !== 'undefined' ? value : '';
//错误提示语
const inputErrorMsg = isRequire ? (typeof errorMsg !== 'undefined' ? errorMsg : '请输入1') : '';
const inputWidth = typeof width !== 'undefined' ? width : '100%';
const inputOnChange = typeof onChange !== 'undefined' ? onChange : null;
//formName 用于最终获取input值的唯一key
const inputName = typeof  formName !== 'undefined' ? formName : null;

const formType = typeof type !== 'undefined' ? type : null;


const tips = typeof tipText !== 'undefined' ? tipText : null;
// 生成唯一ID
const inputId = 'input-' + Math.random().toString(36).substr(2, 9);
const wrapperId = 'wrapper-' + Math.random().toString(36).substr(2, 9);

const contactClass = typeof className !== 'undefined' ? className : '';

// 根据是否有错误信息决定是否添加error类
const errorClass = inputErrorMsg ? ' ' : '';
const filledClass = inputValue ? ' ' : '';
%>


<div id="<%= wrapperId %>" class="input-group form-item  input-single<%= filledClass %> from<%= errorClass %>  <%= contactClass %>" data-width="<%= inputWidth %>">
    <div class="label-wrap">
        <label for="<%= inputId %>"><%= inputLabel %></label>
        <% if(isRequire) { %>
            <span class="require" aria-required="true">*</span>
        <% } %>
        <% if(tips){ %>
            <div role="tooltip"
                 tabindex="0"
                 aria-label="提示信息"
                 class="prompt-bubble tips"
                 data-text="<%= tips %>">
                <span class="icon-zh icon-zh-ask" aria-hidden="true"></span>
            </div>
        <% } %>
    </div>
    <input
            aria-label="<%= inputLabel %>"
            type="text"
            data-inputType="text"
            id="<%= inputId %>"
            class="text form-input"
            placeholder="Please enter <%= inputLabel %>"
            value="<%= inputValue %>"
            data-citycode=""
            data-name="<%= inputName %>"
            data-type="<%= formType %>"
            data-required="<%= isRequire %>"
            oninput="validateField(this)"
            onblur="validateField(this)"
            data-errorMsg="<%= inputErrorMsg %>"
            <% if (inputOnChange) { %>data-onchange="<%= inputOnChange %>"
            <% } %>
    />
    <%
    if(isRequire){
    %>
        <div class="error-tips" aria-label="错误提示">
            <span class="icon-zh icon-zh-error"></span>
            <span class="error-content" aria-label="<%= inputErrorMsg %>"><%= inputErrorMsg %></span>
        </div>
    <% } %>
</div>