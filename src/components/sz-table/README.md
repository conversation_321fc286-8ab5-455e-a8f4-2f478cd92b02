# SZ Table 组件

一个类似 Ant Design Table 的表格组件，支持行列合并、排序、选择等功能。UI 设计基于 MasterGo 设计稿，具有现代化的视觉效果。

## 设计特点

- **设计稿匹配**：完全按照 MasterGo 设计稿实现的 UI 样式
- **虚线边框**：使用虚线边框 (#DBD7D4) 营造轻盈感
- **品牌色装饰**：表头带有品牌色 (#942531) 下划线装饰
- **圆角设计**：表格四角 8px 圆角，现代化视觉
- **居中对齐**：默认文本居中对齐，符合设计规范
- **16px 字体**：使用 Source Han Sans 16px 字体，清晰易读
- **智能边框**：使用 border-collapse 避免合并单元格时的边框重叠问题

## 基本用法

```html
<!-- 在 HTML 中引入组件模板 -->
<%- include('../components/sz-table/index.ejs', { id: 'my-table', className: 'custom-table' }) %>
```

```javascript
// 初始化表格
const table = new SZTable({
  container: '#my-table',
  columns: [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      sorter: true,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
      width: 80,
      align: 'center',
      sorter: (a, b) => a.age - b.age,
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      render: (text, record) => `<span title="${text}">${text}</span>`,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render: (text, record, index) => `
        <button class="btn-edit" data-id="${record.id}">编辑</button>
        <button class="btn-delete" data-id="${record.id}">删除</button>
      `,
    },
  ],
  dataSource: [
    {
      key: '1',
      name: '张三',
      age: 32,
      address: '北京市朝阳区',
    },
    {
      key: '2',
      name: '李四',
      age: 28,
      address: '上海市浦东新区',
    },
  ],
});
```

## API

### 构造函数参数

| 参数         | 说明                       | 类型     | 默认值                  |
| ------------ | -------------------------- | -------- | ----------------------- |
| container    | 表格容器选择器             | string   | '#sz-table'             |
| columns      | 表格列的配置               | Array    | []                      |
| dataSource   | 数据数组                   | Array    | []                      |
| loading      | 页面是否加载中             | boolean  | false                   |
| rowSelection | 表格行是否可选择           | object   | null                    |
| scroll       | 表格是否可滚动             | object   | null                    |
| size         | 表格大小                   | string   | 'default'               |
| bordered     | 是否展示外边框和列边框     | boolean  | false                   |
| showHeader   | 是否显示表头               | boolean  | true                    |
| locale       | 默认文案设置               | object   | {emptyText: '暂无数据'} |
| notes        | 表格下方的备注数组         | Array    | []                      |
| onChange     | 分页、排序、筛选变化时触发 | function | -                       |

### columns 配置

| 参数      | 说明                                                               | 类型                          | 默认值 |
| --------- | ------------------------------------------------------------------ | ----------------------------- | ------ |
| title     | 列头显示文字                                                       | string                        | -      |
| dataIndex | 列数据在数据项中对应的路径                                         | string                        | -      |
| key       | React 需要的 key，如果已经设置了唯一的 dataIndex，可以忽略这个属性 | string                        | -      |
| width     | 列宽度                                                             | number                        | -      |
| align     | 设置列的对齐方式                                                   | 'left' \| 'right' \| 'center' | 'left' |
| sorter    | 排序函数，本地排序使用一个函数，需要服务端排序可设为 true          | function \| boolean           | -      |
| render    | 生成复杂数据的渲染函数                                             | function(text, record, index) | -      |

### rowSelection 配置

| 参数            | 说明                            | 类型                                                  | 默认值 |
| --------------- | ------------------------------- | ----------------------------------------------------- | ------ |
| selectedRowKeys | 指定选中项的 key 数组           | Array                                                 | []     |
| onChange        | 选中项发生变化时的回调          | function(selectedRowKeys, selectedRows)               | -      |
| onSelect        | 用户手动选择/取消选择某行的回调 | function(record, selected, selectedRows, nativeEvent) | -      |

## 方法

| 方法名           | 说明               | 参数                                   |
| ---------------- | ------------------ | -------------------------------------- |
| updateDataSource | 更新数据源         | (dataSource: Array)                    |
| setLoading       | 设置加载状态       | (loading: boolean)                     |
| getSelectedRows  | 获取选中的行       | -                                      |
| setSelectedRows  | 设置选中的行       | (selectedRowKeys: Array)               |
| setMergedCell    | 设置单个单元格合并 | (rowIndex, colIndex, rowspan, colspan) |
| setMergedCells   | 批量设置合并单元格 | (mergeConfig: Array)                   |
| setMergedHeader  | 设置单个表头合并   | (colIndex, colspan)                    |
| setMergedHeaders | 批量设置合并表头   | (mergeConfig: Array)                   |
| updateNotes      | 更新表格备注       | (notes: Array)                         |
| destroy          | 销毁组件           | -                                      |

## 行列合并功能

### 单个单元格合并

```javascript
// 合并第0行第1列的单元格，向下合并2行，向右合并1列
table.setMergedCell(0, 1, 2, 1);
```

### 批量合并单元格

```javascript
const mergeConfig = [
  { row: 0, col: 0, rowspan: 2, colspan: 1 }, // 第0行第0列，合并2行1列
  { row: 0, col: 1, rowspan: 1, colspan: 2 }, // 第0行第1列，合并1行2列
  { row: 2, col: 0, rowspan: 1, colspan: 3 }, // 第2行第0列，合并1行3列
];

table.setMergedCells(mergeConfig);
```

## 表头合并功能

### 单个表头合并

```javascript
// 合并第0列开始的2列表头
table.setMergedHeader(0, 2);
```

### 批量合并表头

```javascript
const headerMergeConfig = [
  { col: 0, colspan: 2 }, // 第0列开始，合并2列
  { col: 3, colspan: 2 }, // 第3列开始，合并2列
];

table.setMergedHeaders(headerMergeConfig);
```

### 表头合并 + 数据合并组合使用

```javascript
// 设置表头合并
table.setMergedHeaders([
  { col: 0, colspan: 2 }, // 合并前两列表头
]);

// 设置数据合并
table.setMergedCells([
  { row: 0, col: 0, rowspan: 2, colspan: 1 }, // 合并数据单元格
]);
```

## 表格备注功能

表格支持在下方显示多个段落备注，用于补充说明表格内容。

### 基本用法

```javascript
const table = new SZTable({
  container: '#my-table',
  columns: [...],
  dataSource: [...],
  notes: [
    '备注1：此表格数据仅供参考，实际情况可能有所不同。',
    '备注2：价格信息更新时间为2024年1月1日。',
    '备注3：如有疑问请联系客服。'
  ]
});
```

### 动态更新备注

```javascript
// 更新备注内容
table.updateNotes([
  '新的备注信息1',
  '新的备注信息2'
]);

// 清空备注
table.updateNotes([]);
```

## 完整示例

```javascript
const table = new SZTable({
  container: '#example-table',
  columns: [
    {
      title: '产品名称',
      dataIndex: 'product',
      key: 'product',
      width: 150,
    },
    {
      title: '规格',
      dataIndex: 'spec',
      key: 'spec',
      width: 100,
      align: 'center',
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      align: 'right',
      sorter: (a, b) => a.quantity - b.quantity,
    },
    {
      title: '单价',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      align: 'right',
      render: text => `¥${text}`,
    },
  ],
  dataSource: [
    { key: '1', product: '苹果', spec: '大', quantity: 10, price: 5.0 },
    { key: '2', product: '苹果', spec: '小', quantity: 20, price: 3.0 },
    { key: '3', product: '橙子', spec: '中', quantity: 15, price: 4.0 },
  ],
  notes: [
    '备注：以上价格为市场参考价，实际价格可能因地区而异。',
    '说明：数量单位为公斤，价格单位为元/公斤。'
  ],
  rowSelection: {
    selectedRowKeys: [],
    onChange: (selectedRowKeys, selectedRows) => {
      console.log('选中的行:', selectedRowKeys, selectedRows);
    },
  },
});

// 合并相同产品的单元格
table.setMergedCells([
  { row: 0, col: 0, rowspan: 2, colspan: 1 }, // 合并前两行的产品名称列
]);
```

## 边框处理

组件采用了优化的边框策略来处理合并单元格时的边框重叠问题：

- 使用 `border-collapse: collapse` 避免边框重叠
- 所有单元格统一使用 `border: 1px dashed @gray-1`
- 合并单元格时边框会自动正确处理，不会出现加粗现象

## 样式定制

组件使用 Less 编写样式，可以通过覆盖 CSS 变量来定制样式：

```less
.sz-table-wrapper {
  // 自定义表格样式
  .sz-table {
    // 自定义表格内部样式

    th,
    td {
      // 自定义边框样式
      border: 1px solid #your-color;
    }
  }
}
```
