<% const _id = typeof id !== 'undefined' ? id: 'sz-table' %>
<% const _className = typeof className !== 'undefined' ? className: '' %>
<% const _notes = typeof notes !== 'undefined' ? notes: [] %>

<div class="sz-table" id="<%= _id %>">
  <div class="sz-table-wrapper <%= _className %>">
    <div class="sz-table-container">
      <table class="sz-table" role="table">
        <thead class="sz-table-thead" role="rowgroup">
          <!-- 表头将通过 JavaScript 动态生成 -->
        </thead>
        <tbody class="sz-table-tbody" role="rowgroup">
          <!-- 表体将通过 JavaScript 动态生成 -->
        </tbody>
      </table>
    </div>

    <!-- 空状态 -->
    <div class="sz-table-empty" style="display: none">
      <div class="sz-table-empty-content">
        <!-- <img src="../../images/empty-data.png" alt="暂无数据" class="sz-table-empty-image" /> -->
        <div class="sz-table-empty-text">暂无数据</div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="sz-table-loading" style="display: none">
      <div class="sz-table-loading-content">
        <div class="sz-table-loading-spinner"></div>
        <div class="sz-table-loading-text">加载中...</div>
      </div>
    </div>
  </div>

  <!-- 表格备注 -->
  <div class="sz-table-notes">
    <!-- 备注内容将通过 JavaScript 动态生成 -->
    <% _notes.forEach((item)=>{ %>
    <p class="sz-table-note-item"><%= item %></p>
    <% }); %>
  </div>
</div>
