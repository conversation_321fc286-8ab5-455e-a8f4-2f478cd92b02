/**
 * SZ Table 组件
 * 类似 Ant Design Table 的表格组件，支持行列合并
 */

class SZTable {
  constructor(options = {}) {
    this.container = options.container || '#sz-table';
    this.columns = options.columns || [];
    this.dataSource = options.dataSource || [];
    this.loading = options.loading || false;
    this.pagination = options.pagination || false;
    this.rowSelection = options.rowSelection || null;
    this.scroll = options.scroll || null;
    this.size = options.size || 'default'; // default, small, large
    this.bordered = options.bordered || false;
    this.showHeader = options.showHeader !== false;
    this.locale = options.locale || { emptyText: '暂无数据' };
    this.notes = options.notes || []; // 表格下方的备注数组

    // 排序相关
    this.sortedInfo = options.sortedInfo || {};
    this.onChange = options.onChange || (() => {});

    // 行列合并相关
    this.mergedCells = new Map(); // 存储合并单元格信息
    this.mergedHeaders = new Map(); // 存储合并表头信息

    this.$container = $(this.container);

    // 检查容器是否存在
    if (!this.$container.length) {
      console.error(`SZTable: Container "${this.container}" not found`);
      return;
    }

    this.$table = this.$container.find('.sz-table');
    this.$thead = this.$container.find('.sz-table-thead');
    this.$tbody = this.$container.find('.sz-table-tbody');
    this.$empty = this.$container.find('.sz-table-empty');
    this.$loading = this.$container.find('.sz-table-loading');
    this.$notes = this.$container.find('.sz-table-notes');

    // 检查必要的DOM元素
    if (!this.$table.length || !this.$thead.length || !this.$tbody.length) {
      console.error(
        'SZTable: Required DOM elements not found. Please ensure the table template is correctly included.'
      );
      return;
    }

    this.init();
  }

  init() {
    this.render();
    this.bindEvents();
  }

  /**
   * 渲染表格
   */
  render() {
    this.renderHeader();
    this.renderBody();
    this.renderNotes();
    this.toggleLoading();
    this.toggleEmpty();
  }

  /**
   * 渲染表头
   */
  renderHeader() {
    if (!this.showHeader) {
      this.$thead.hide();
      return;
    }

    let headerHtml = '<tr>';

    // 如果有行选择功能，添加选择列
    if (this.rowSelection) {
      headerHtml += `
        <th class="sz-table-selection-column">
          <input type="checkbox" class="sz-table-select-all" />
        </th>
      `;
    }

    this.columns.forEach((column, colIndex) => {
      const mergedInfo = this.getMergedHeaderInfo(colIndex);

      // 如果这个表头被合并了，跳过渲染
      if (mergedInfo.skip) {
        return;
      }

      const sortable = column.sorter ? 'sz-table-cell-sortable' : '';
      const align = column.align ? `sz-table-cell-${column.align}` : 'sz-table-cell-center';
      const width = column.width ? `style="width: ${column.width}px; min-width: ${column.width}px;"` : '';
      const mergedClass = mergedInfo.colspan > 1 ? 'sz-table-cell-merged' : '';
      const colspanAttr = mergedInfo.colspan > 1 ? `colspan="${mergedInfo.colspan}"` : '';

      let sortIcon = '';
      if (column.sorter) {
        const sortOrder = this.sortedInfo.columnKey === column.key ? this.sortedInfo.order : null;
        const iconClass = sortOrder ? `sz-table-sort-icon ${sortOrder}` : 'sz-table-sort-icon';
        sortIcon = `<span class="${iconClass}"></span>`;
      }

      headerHtml += `
        <th class="${sortable} ${align} ${mergedClass}" data-key="${column.key}" ${width} ${colspanAttr}>
          ${column.title ? column.title : ''}${sortIcon}
        </th>
      `;
    });

    headerHtml += '</tr>';
    this.$thead.html(headerHtml);
  }

  /**
   * 渲染表体
   */
  renderBody() {
    if (!this.dataSource.length) {
      this.$tbody.empty();
      return;
    }

    let bodyHtml = '';

    this.dataSource.forEach((record, rowIndex) => {
      const rowKey = record.key || rowIndex;
      const selectedClass = this.isRowSelected(rowKey) ? 'sz-table-row-selected' : '';

      bodyHtml += `<tr data-row-key="${rowKey}" class="${selectedClass}">`;

      // 如果有行选择功能，添加选择列
      if (this.rowSelection) {
        const checked = this.isRowSelected(rowKey) ? 'checked' : '';
        bodyHtml += `
          <td class="sz-table-selection-column">
            <input type="checkbox" class="sz-table-row-select" data-row-key="${rowKey}" ${checked} />
          </td>
        `;
      }

      this.columns.forEach((column, colIndex) => {
        const cellKey = `${rowIndex}-${colIndex}`;
        const mergedInfo = this.getMergedCellInfo(rowIndex, colIndex);

        // 如果这个单元格被合并了，跳过渲染
        if (mergedInfo.skip) {
          return;
        }

        const align = column.align ? `sz-table-cell-${column.align}` : 'sz-table-cell-center'; // 默认居中
        const mergedClass = mergedInfo.rowspan > 1 || mergedInfo.colspan > 1 ? 'sz-table-cell-merged' : '';
        const rowspanAttr = mergedInfo.rowspan > 1 ? `rowspan="${mergedInfo.rowspan}"` : '';
        const colspanAttr = mergedInfo.colspan > 1 ? `colspan="${mergedInfo.colspan}"` : '';

        // 检测合并单元格是否位于表格边缘，添加相应的边框移除类
        let edgeClasses = '';
        if (mergedInfo.rowspan > 1 || mergedInfo.colspan > 1) {
          // 检测是否位于左边缘
          if (colIndex === 0) {
            edgeClasses += ' sz-table-cell-left-edge';
          }
          // 检测是否位于右边缘
          if (colIndex + mergedInfo.colspan >= this.columns.length) {
            edgeClasses += ' sz-table-cell-right-edge';
          }
          // 检测是否位于上边缘（表头下方第一行）
          if (rowIndex === 0) {
            edgeClasses += ' sz-table-cell-top-edge';
          }
          // 检测是否位于下边缘
          if (rowIndex + mergedInfo.rowspan >= this.dataSource.length) {
            edgeClasses += ' sz-table-cell-bottom-edge';
          }
        }

        let cellContent = '';
        if (column.render && typeof column.render === 'function') {
          cellContent = column.render(record[column.dataIndex], record, rowIndex);
        } else {
          cellContent = record[column.dataIndex] || '';
        }

        bodyHtml += `
          <td class="${align} ${mergedClass}${edgeClasses}" ${rowspanAttr} ${colspanAttr} data-cell-key="${cellKey}">
            ${cellContent}
          </td>
        `;
      });

      bodyHtml += '</tr>';
    });

    this.$tbody.html(bodyHtml);
  }

  /**
   * 渲染备注
   */
  renderNotes() {
    if (this.notes && Array.isArray(this.notes) && this.notes.length > 0) {
      let notesHtml = '';
      this.notes.forEach((note, index) => {
        if (note && typeof note === 'string') {
          notesHtml += `<p class="sz-table-note-item">${note}</p>`;
        }
      });

      this.$notes.html(notesHtml);
    }
  }

  /**
   * 获取合并单元格信息
   */
  getMergedCellInfo(rowIndex, colIndex) {
    const key = `${rowIndex}-${colIndex}`;
    return this.mergedCells.get(key) || { rowspan: 1, colspan: 1, skip: false };
  }

  /**
   * 获取合并表头信息
   */
  getMergedHeaderInfo(colIndex) {
    const key = `${colIndex}`;
    return this.mergedHeaders.get(key) || { colspan: 1, skip: false };
  }

  /**
   * 设置单元格合并
   * @param {number} rowIndex - 行索引
   * @param {number} colIndex - 列索引
   * @param {number} rowspan - 合并行数
   * @param {number} colspan - 合并列数
   */
  setMergedCell(rowIndex, colIndex, rowspan = 1, colspan = 1) {
    // 参数验证
    if (rowIndex < 0 || colIndex < 0 || rowspan < 1 || colspan < 1) {
      console.warn('SZTable: Invalid merge parameters', { rowIndex, colIndex, rowspan, colspan });
      return;
    }

    // 检查边界
    if (rowIndex + rowspan > this.dataSource.length || colIndex + colspan > this.columns.length) {
      console.warn('SZTable: Merge parameters exceed table bounds', {
        rowIndex,
        colIndex,
        rowspan,
        colspan,
        maxRows: this.dataSource.length,
        maxCols: this.columns.length,
      });
      return;
    }

    const key = `${rowIndex}-${colIndex}`;
    this.mergedCells.set(key, { rowspan, colspan, skip: false });

    // 标记被合并的单元格为跳过渲染
    for (let r = rowIndex; r < rowIndex + rowspan; r++) {
      for (let c = colIndex; c < colIndex + colspan; c++) {
        if (r !== rowIndex || c !== colIndex) {
          const skipKey = `${r}-${c}`;
          this.mergedCells.set(skipKey, { rowspan: 1, colspan: 1, skip: true });
        }
      }
    }
  }

  /**
   * 批量设置合并单元格
   * @param {Array} mergeConfig - 合并配置数组 [{row, col, rowspan, colspan}]
   */
  setMergedCells(mergeConfig) {
    if (!Array.isArray(mergeConfig)) {
      console.warn('SZTable: mergeConfig must be an array');
      return;
    }

    this.mergedCells.clear();
    mergeConfig.forEach(config => {
      if (config && typeof config === 'object') {
        this.setMergedCell(config.row, config.col, config.rowspan || 1, config.colspan || 1);
      }
    });
    this.renderBody();
  }

  /**
   * 设置表头合并
   * @param {number} colIndex - 列索引
   * @param {number} colspan - 合并列数
   */
  setMergedHeader(colIndex, colspan = 1) {
    // 参数验证
    if (colIndex < 0 || colspan < 1) {
      console.warn('SZTable: Invalid header merge parameters', { colIndex, colspan });
      return;
    }

    // 检查边界
    if (colIndex + colspan > this.columns.length) {
      console.warn('SZTable: Header merge parameters exceed table bounds', {
        colIndex,
        colspan,
        maxCols: this.columns.length,
      });
      return;
    }

    const key = `${colIndex}`;
    this.mergedHeaders.set(key, { colspan, skip: false });

    // 标记被合并的表头为跳过渲染
    for (let c = colIndex + 1; c < colIndex + colspan; c++) {
      const skipKey = `${c}`;
      this.mergedHeaders.set(skipKey, { colspan: 1, skip: true });
    }
  }

  /**
   * 批量设置合并表头
   * @param {Array} mergeConfig - 合并配置数组 [{col, colspan}]
   */
  setMergedHeaders(mergeConfig) {
    if (!Array.isArray(mergeConfig)) {
      console.warn('SZTable: mergeConfig must be an array');
      return;
    }

    this.mergedHeaders.clear();
    mergeConfig.forEach(config => {
      if (config && typeof config === 'object') {
        this.setMergedHeader(config.col, config.colspan || 1);
      }
    });
    this.renderHeader();
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 排序事件
    this.$thead.on('click', '.sz-table-cell-sortable', e => {
      const $th = $(e.currentTarget);
      const columnKey = $th.data('key');
      const column = this.columns.find(col => col.key === columnKey);

      if (!column || !column.sorter) return;

      let order = 'ascend';
      if (this.sortedInfo.columnKey === columnKey) {
        if (this.sortedInfo.order === 'ascend') {
          order = 'descend';
        } else if (this.sortedInfo.order === 'descend') {
          order = null;
        }
      }

      this.sortedInfo = order ? { columnKey, order } : {};

      // 如果有自定义排序函数，执行排序
      if (order && typeof column.sorter === 'function') {
        this.dataSource.sort((a, b) => {
          const result = column.sorter(a, b);
          return order === 'descend' ? -result : result;
        });
        this.renderBody();
      }

      this.renderHeader();

      // 触发 onChange 回调
      this.onChange(null, null, this.sortedInfo);
    });

    // 全选事件
    this.$thead.on('change', '.sz-table-select-all', e => {
      const checked = e.target.checked;
      this.$tbody.find('.sz-table-row-select').prop('checked', checked);
      this.updateRowSelection();
    });

    // 行选择事件
    this.$tbody.on('change', '.sz-table-row-select', () => {
      this.updateRowSelection();
    });

    // 行点击事件
    if (this.rowSelection) {
      this.$tbody.on('click', 'tr', e => {
        if ($(e.target).is('input[type="checkbox"]')) return;

        const $row = $(e.currentTarget);
        const $checkbox = $row.find('.sz-table-row-select');

        if ($checkbox.length) {
          $checkbox.prop('checked', !$checkbox.prop('checked'));
          this.updateRowSelection();
        }
      });
    }
  }

  /**
   * 更新行选择状态
   */
  updateRowSelection() {
    if (!this.rowSelection) return;

    const selectedRowKeys = [];
    const selectedRows = [];

    this.$tbody.find('.sz-table-row-select:checked').each((_, checkbox) => {
      const rowKey = $(checkbox).data('row-key');
      const record = this.dataSource.find(item => (item.key || this.dataSource.indexOf(item)) == rowKey);

      selectedRowKeys.push(rowKey);
      if (record) selectedRows.push(record);
    });

    // 更新全选状态
    const totalRows = this.dataSource.length;
    const selectedCount = selectedRowKeys.length;
    const $selectAll = this.$thead.find('.sz-table-select-all');

    if (selectedCount === 0) {
      $selectAll.prop('checked', false).prop('indeterminate', false);
    } else if (selectedCount === totalRows) {
      $selectAll.prop('checked', true).prop('indeterminate', false);
    } else {
      $selectAll.prop('checked', false).prop('indeterminate', true);
    }

    // 更新行样式
    this.$tbody.find('tr').removeClass('sz-table-row-selected');
    selectedRowKeys.forEach(key => {
      this.$tbody.find(`tr[data-row-key="${key}"]`).addClass('sz-table-row-selected');
    });

    // 更新 rowSelection 中的 selectedRowKeys
    this.rowSelection.selectedRowKeys = selectedRowKeys;

    // 触发选择回调
    if (this.rowSelection.onChange) {
      this.rowSelection.onChange(selectedRowKeys, selectedRows);
    }
  }

  /**
   * 检查行是否被选中
   */
  isRowSelected(rowKey) {
    if (!this.rowSelection || !this.rowSelection.selectedRowKeys) return false;
    return this.rowSelection.selectedRowKeys.includes(rowKey);
  }

  /**
   * 切换加载状态
   */
  toggleLoading() {
    if (this.loading) {
      this.$loading.show();
    } else {
      this.$loading.hide();
    }
  }

  /**
   * 切换空状态
   */
  toggleEmpty() {
    if (!this.loading && !this.dataSource.length) {
      this.$empty.show();
      this.$table.hide();
    } else {
      this.$empty.hide();
      this.$table.show();
    }
  }

  /**
   * 更新数据源
   */
  updateDataSource(dataSource) {
    if (!Array.isArray(dataSource)) {
      console.warn('SZTable: dataSource must be an array');
      return;
    }

    this.dataSource = dataSource;
    // 清除可能无效的合并单元格配置
    if (this.mergedCells.size > 0) {
      const validMergedCells = new Map();
      this.mergedCells.forEach((value, key) => {
        const [rowIndex, colIndex] = key.split('-').map(Number);
        if (rowIndex < dataSource.length && colIndex < this.columns.length) {
          validMergedCells.set(key, value);
        }
      });
      this.mergedCells = validMergedCells;
    }

    this.render();
  }

  /**
   * 更新备注
   */
  updateNotes(notes) {
    // if (!Array.isArray(notes)) {
    //   console.warn('SZTable: notes must be an array');
    //   return;
    // }

    this.notes = notes;
    this.renderNotes();
  }

  /**
   * 设置加载状态
   */
  setLoading(loading) {
    this.loading = loading;
    this.toggleLoading();
  }

  /**
   * 获取选中的行
   */
  getSelectedRows() {
    if (!this.rowSelection) return { selectedRowKeys: [], selectedRows: [] };

    const selectedRowKeys = [];
    const selectedRows = [];

    this.$tbody.find('.sz-table-row-select:checked').each((_, checkbox) => {
      const rowKey = $(checkbox).data('row-key');
      const record = this.dataSource.find(item => (item.key || this.dataSource.indexOf(item)) == rowKey);

      selectedRowKeys.push(rowKey);
      if (record) selectedRows.push(record);
    });

    return { selectedRowKeys, selectedRows };
  }

  /**
   * 设置选中的行
   */
  setSelectedRows(selectedRowKeys) {
    if (!this.rowSelection) return;

    this.rowSelection.selectedRowKeys = selectedRowKeys;
    this.$tbody.find('.sz-table-row-select').each((index, checkbox) => {
      const rowKey = $(checkbox).data('row-key');
      $(checkbox).prop('checked', selectedRowKeys.includes(rowKey));
    });

    this.updateRowSelection();
  }

  /**
   * 销毁组件
   */
  destroy() {
    this.$container.off();
    this.mergedCells.clear();
    this.mergedHeaders.clear();
  }
}
