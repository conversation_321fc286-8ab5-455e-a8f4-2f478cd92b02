@import 'variables';
@import 'mediaMixin.less';
// grid
@import './grid.less';

@import './reset';

@import './iconfont.less';
@import './button.less';
@import './base.less';

// components
@import '../components/input/input.less';
@import '../components/dropdown-select/index.less';
@import '../components/checkBox/checkbox.less';
@import '../components/bubble/bubble.less';
@import '../components/sz-modal/index.less'; // modal
@import '../components/sz-price-calendar/PriceCalendar.less'; // price-calendar
@import '../components/sz-table/index.less'; // table

// plugins
@import '../plugins/citySelect/citySelect.less';
@import '../plugins/datepicker/datepicker.less';

// 公共模块
@import '../modules/header/header.less';
@import '../modules/footer/footer.less';
@import '../modules/showSpinner/showSpinner.less'; // loading
@import '../modules/step/index.less'; // 步骤条
@import '../modules/priceSummaryCard/index.less'; // 总价卡片

// ---------------------------------------------------------------------------------------------------------------

.sz {
  min-height: 100vh;

  &-banner {
    width: 100%;
    height: 380px;
    position: relative;
    z-index: -1;
    object-fit: cover;

    .screenPad({
      height: 265px;
      });

    .screenMobile({
      height: 130px;
    });
  }

  &-banner_min {
    width: 100%;
    height: 260px;
    position: relative;
    z-index: -1;
    object-fit: cover;

    .screenPad({
      height: 250px;
      });

    .screenMobile({
      height: 225px;
    });
  }

  &-container {
    max-width: 1380px;
    margin: -80px auto 136px;
    padding: 0 20px;

    .screenPad({
       padding: 0 20px;
    });

    .screenMobile({
      padding: 0 15px;
    });
  }

  &-container_min {
    max-width: 1380px;
    margin: -220px auto 136px;
    padding: 0 20px;

    .screenPad({
      padding: 0 20px;
      margin: -190px auto 136px;
    });

    .screenMobile({
      padding: 0 15px;
      margin: -190px auto 136px;
    });
  }

  //校验的必填星号样式 和提示样式 form表单的
  .form-item {
    .require {
      color: @brand-1;
    }

    .prompt-bubble {
      .icon-zh-ask {
        color: @brand-1;
      }
    }
  }
}

.tippy-box {
  background: @gray-0;
  color: @gray-3;
  font-size: 16px;
  font-weight: 400;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 690px !important;
  padding: 25px 20px;

  .screenMobile({
    max-width: 325px !important;
    padding: 12px 10px;

    font-size: 12px;
  });

  .tippy-content {
    padding: 0;
  }

  &[data-placement^='top'] > .tippy-arrow::before {
    border-top-color: @gray-0;
  }
  &[data-placement^='bottom'] > .tippy-arrow::before {
    border-bottom-color: @gray-0;
  }
  &[data-placement^='left'] > .tippy-arrow::before {
    border-left-color: @gray-0;
  }
  &[data-placement^='right'] > .tippy-arrow::before {
    border-right-color: @gray-0;
  }
}
