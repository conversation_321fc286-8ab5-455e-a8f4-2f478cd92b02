/* ========================================================================
 * HX: citySelect  v1.0.0  城市组件
 * Author: zjjing
 * ========================================================================
 * Copyright 2024-~ Travelsky ICED
 * response citySelect compoment
 * ======================================================================== */
+(function ($) {
  // fn方式封装插件
  var curCitySelectInput;
  $.fn.citySelect = function (options) {
    var inputC = this; // 输入框

    // 设置插件默认属性
    var defaults = {
      cityData: {}, // 城市数据
      defaultVal: '', // 城市初始化的默认值
      container: '<div class="city-panel"></div>', // 最外层容器
      continentTitle: '', // 第一列tittle
      backTo: '', // 选中第一列数据后，第二列的tittle
      parenClass: '.city-origin', // 需要添加show-panel的父级类名，可以自己配置，默认为city-origin
      noCity: '我們未能找到相符的目的地', // 没找到机场/城市显示的信息
      isHotCity: true, // 是否有热门城市，模糊搜索需要过滤热门城市，因为后面的数据已经包含了
    };
    var setting = $.extend({}, defaults, options);

    // 初始化对象
    var citySelect = {
      data: {
        cityData: setting.cityData, // 初始城市数据对象
        inputVal: '', // 控件对应的输入值
      },

      initData: function () {
        // 渲染Dom结构
        citySelect.renderHtml();
        // 如果有默认值
        if (setting.defaultVal) {
          $(inputC).val(setting.defaultVal);
        }
      },

      // 初始化方法调用
      init: function () {
        this.initData();
        this.bindEvent();
      },

      // 关闭插件
      closeComponent: function () {
        $(document.body).find(setting.parenClass).removeClass('show-panel');
        $(document.body)
          .find(setting.parenClass + ' .show-airport')
          .removeClass('show-airport');
        $(document.body)
          .find(setting.parenClass + ' .show-search')
          .removeClass('show-search');
        $(document.body)
          .find(setting.parenClass + ' .city-continent')
          .removeClass('dn');
        $(document.body)
          .find(setting.parenClass + ' .active')
          .removeClass('active');
      },

      // 渲染插件Html
      renderHtml: function () {
        // 渲染第一列表格数据，洲
        var html = '';
        html +=
          '<div class="city-render">' +
          citySelect.renderContinent() +
          citySelect.renderAirport() +
          citySelect.renderSearch() +
          '</div>';
        $(inputC).closest(setting.parenClass).append($(setting.container).append(html));
      },

      // 创建第一列城市数据，洲
      renderContinent: function () {
        var continent = '';
        continent +=
          '<div class="city-continent">' +
          '<div class="continent-title">' +
          setting.continentTitle +
          '</div>' +
          citySelect.renderUnit(true) +
          '</div>';

        return continent;
      },

      // 创建第二列城市数据，机场
      renderAirport: function () {
        var airport = '';
        airport +=
          '<div class="city-airport">' +
          '<div class="back-continent"><span role="button" class="back-to">' +
          '<span class="icon-zh icon-zh-left"></span>' +
          '<span>' +
          setting.backTo +
          '</span>' +
          '</span></div>' +
          citySelect.renderUnit(false) +
          '</div>';

        return airport;
      },

      // 创建搜索结果DOM
      renderSearch: function () {
        var search = '';
        var listCity = citySelect.data.cityData.locations; // 本地存储的数据
        var tempOptions = [];
        var searchText = citySelect.data.inputVal.toLowerCase();
        // 如果为热门城市，则去除热门城市数据进行查找,避免重复数据显示
        if (setting.isHotCity) {
          listCity = listCity.filter((el, index) => index !== 0);
        }
        // 如果有input有输入值则进行查询
        if (searchText) {
          // 将所有的数据融合到tempOptions中
          for (var op of listCity) {
            tempOptions.push(...op.options);
          }
          // 通过输入的值进行遍历，找到对应的数据准备渲染
          var lists = tempOptions.filter(list => {
            return (
              list.city?.toLowerCase().indexOf(searchText) !== -1 || list.code?.toLowerCase().indexOf(searchText) !== -1
            );
          });
          // 如果有数据，则渲染
          if (lists.length) {
            search += '<div class="city-result"><ul role="listbox" class="city-list-wrap">';
            for (var l of lists) {
              search +=
                '<li class="city-list" role="option">' +
                '<span class="city-text">' +
                l.city +
                ', ' +
                ' (' +
                l.code +
                ') ' +
                '</span>' +
                '<span class="city-con">' +
                '</span>' +
                '</li>';
            }
            search += '</ul></div>';
          } else {
            // 没有数据则渲染未找到
            search += '<div class="city-result">' + '<span class="no-result">' + setting.noCity + '</span>' + '</div>';
          }
        } else {
          // 没有输入值，则初始化渲染
          search +=
            '<div class="city-search">' +
            '<div class="city-result">' +
            '<span class="no-result">' +
            setting.noCity +
            '</span>' +
            '</div>' +
            '</div>';
        }

        return search;
      },

      // 渲染city-list-wrap
      renderUnit: function (isContinent, dataRef) {
        var listWrap = '';
        var locations = citySelect.data.cityData.locations;
        var firstGroupData = locations[0]; // 先取第一组数据渲染dom结构
        var targetGroup = locations.filter(_ => dataRef === _.group)[0]; // 找到需要渲染的数据
        if (isContinent) {
          listWrap += '<ul role="listbox" class="city-list-wrap">';
          for (var item of locations) {
            listWrap +=
              '<li class="city-list" role="option">' +
              '<span class="city-text">' +
              item.group +
              '</span>' +
              '<span class="icon-zh icon-zh-right-type1 city-con">' +
              '</span>' +
              '</li>';
          }
          listWrap += '</ul>';
        } else {
          // 默认为组内第一数据
          listWrap += '<ul role="listbox" class="city-list-wrap">';
          for (var data of dataRef ? targetGroup.options : firstGroupData.options) {
            listWrap +=
              '<li class="city-list" role="option">' +
              '<span class="city-text">' +
              data.city +
              ', ' +
              ' (' +
              data.code +
              ') ' +
              '</span>' +
              '<span class="icon-zh icon-zh-flight-other city-con">' +
              '</span>' +
              '</li>';
          }
          listWrap += '</ul>';
        }
        return listWrap;
      },

      // 根据code拿到group
      getGroupByCode: function (code) {
        for (const location of citySelect.data.cityData.locations) {
          if (location.isHotCity) continue; // 跳过热门城市
          for (const option of location.options) {
            if (option.code === code) {
              return location.group;
            }
          }
        }
        return ''; // 如果没有找到对应的 group，返回空字符串
      },

      bindEvent: function () {
        var that = this;

        // 点击激活城市控件
        $(inputC).on('click', function () {
          curCitySelectInput = $(this);
          that.closeComponent();
          $(this).closest(setting.parenClass).addClass('show-panel');
        });

        // 给input框加键盘keydown事件
        $(inputC).on('keydown', function (event) {
          curCitySelectInput = $(this);

          event.stopPropagation(); // 阻止冒泡

          // 回车激活组件
          if (event.key === 'Enter') {
            that.closeComponent();
            $(this).closest(setting.parenClass).addClass('show-panel');
          }
          // tab按钮时直接往后进行，关闭当前组件
          if (event.key === 'Tab') {
            that.closeComponent();
          }
          // 组件打开时，键盘上、下键进入组件内部
          if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
            var showPanel = $(this).closest(setting.parenClass).is('.show-panel'); // 组件是否激活
            var showSearch = $(this).closest(setting.parenClass).find('.city-search').is('.show-search'); // 判断是否有打开search
            if (showPanel && !showSearch) {
              // $(this).blur(); // 先失去焦点再添加焦点会导致旁白阅读时焦点出错，执行不下去，所以用tabindex来进行处理
              $(this)
                .closest(setting.parenClass)
                .find('.city-continent .city-list-wrap .city-list')
                .first()
                .addClass('active')
                .attr('tabindex', 0)
                .focus(); // 增加新的焦点
            }
            if (showPanel && showSearch) {
              $(this)
                .closest(setting.parenClass)
                .find('.city-search .city-list-wrap .city-list')
                .first()
                .addClass('active')
                .attr('tabindex', 0)
                .focus(); // 增加新的焦点
            }
          }
          // 点击esc按钮，先退出二级，再退出组件
          if (event.key === 'Escape') {
            var air = $(this).closest(setting.parenClass).find('.show-airport');
            if (air.length) {
              air.removeClass('show-airport');
              air.siblings().removeClass('dn');
            } else {
              that.closeComponent();
            }
          }
          // 点击Backspace/Delete
          if (event.key === 'Backspace' || event.key === 'Delete') {
            $(this).attr('data-citycode', '');
          }
        });

        // 给input框加键盘keyup事件
        $(inputC).on('keyup', function () {
          curCitySelectInput = $(this);

          var input = this;
          // 拿到输入的值去查找对应的数据
          if ($(input).val() === '') {
            $(this).closest(setting.parenClass).find('.city-search').removeClass('show-search');
            $(this).closest(setting.parenClass).find('.city-continent').removeClass('dn');
            // 清空data-citycode值
            $(this).attr('data-citycode', '');
          } else {
            that.data.inputVal = $.trim($(input).val()); // 输入框的值
            var searchData = that.renderSearch(); // 根据输入的值创建新的option
            $(this).closest(setting.parenClass).addClass('show-panel');
            $(this).closest(setting.parenClass).find('.city-search .city-result').remove();
            $(this).closest(setting.parenClass).find('.city-search').append(searchData).addClass('show-search');
            $(this).closest(setting.parenClass).find('.city-continent').addClass('dn');
          }
        });

        // 点击dom关闭插件
        $(document).on('click', function (e) {
          var cityPanel = $(e.target).closest(setting.parenClass).length;
          $(this).closest(setting.parenClass).hasClass('show-panel');
          if (!cityPanel) {
            if (
              curCitySelectInput &&
              curCitySelectInput.closest(setting.parenClass).hasClass('show-panel') &&
              curCitySelectInput.closest(setting.parenClass).find('.city-search').hasClass('show-search')
            ) {
              let searchCityData = curCitySelectInput
                .closest(setting.parenClass)
                .find('.city-search')
                .find('.city-list');

              if (searchCityData.length === 1) {
                const cityText = searchCityData.first().find('.city-text').text();
                const city = cityText.split(',')[0]; // 拿到city
                const code = cityText.match(/\(.*?\)/g)[0].replace(/[()]/g, ''); // 拿到code，并去除括号
                const targetGroup = that.getGroupByCode(code); // 找到需要渲染的数据
                const val = city + ', ' + targetGroup; // 拼接显示的数据
                curCitySelectInput.val(val);
                curCitySelectInput.attr('data-citycode', code);
              } else {
                curCitySelectInput.val('');
                curCitySelectInput.attr('data-citycode', '');
              }
              curCitySelectInput = null;
            }
            that.closeComponent();
          }
        });

        // 增加城市控件鼠标点击事件
        $(document)
          .on('click', setting.parenClass + ' .city-list', function () {
            // 判断是否在第一个列做选择
            var isContinent = $(this).closest('.city-continent').length;
            var cityText = $(this).find('.city-text').text();
            if (isContinent) {
              // 拿到当前点击时的group数据，准备重新渲染第二列数据
              var newData = that.renderUnit(false, cityText); // 重新渲染数据结构
              $(this).closest(setting.parenClass).find('.city-airport .city-list-wrap').remove();
              $(this).closest(setting.parenClass).find('.city-airport').append(newData).addClass('show-airport');
              $(this).closest(setting.parenClass).find('.city-continent').addClass('dn');
            } else {
              // 进行选中操作
              var city = cityText.split(',')[0]; // 拿到city
              var code = cityText.match(/\(.*?\)/g)[0].replace(/[()]/g, ''); // 拿到code，并去除括号

              // 通过code去拼接显示的数据
              var targetGroup = that.getGroupByCode(code); // 找到需要渲染的数据
              var val = city + ', ' + targetGroup; // 拼接显示的数据
              // 赋值 val data-citycode
              $(this)
                .closest(setting.parenClass)
                .find('input')
                .val(val)
                .attr('data-citycode', code.trim().replace(/^\(|\)$/g, ''))
                .change();
              that.closeComponent(); // 关闭控件
            }
          })
          .on('click', setting.parenClass + ' .back-to', function () {
            // 返回第一列
            $(this).closest(setting.parenClass).find('.city-airport').removeClass('show-airport');
            $(this).closest(setting.parenClass).find('.city-continent').removeClass('dn');
          })
          .on('keydown', setting.parenClass + ' .city-list', function (event) {
            // 键盘操作事件
            var thisLi = this;
            var listWrap = $(thisLi).closest('.city-list-wrap'); // list-wrap

            // 回车选中事件
            if (event.key === 'Enter') {
              // 判断是否在第一个列做选择
              var isContinent = $(thisLi).closest('.city-continent').length;
              var cityText = $(thisLi).find('.city-text').text();
              if (isContinent) {
                // 拿到当前点击时的group数据，准备重新渲染第二列数据
                var newData = that.renderUnit(false, cityText); // 重新渲染数据结构
                $(thisLi).closest(setting.parenClass).find('.city-airport .city-list-wrap').remove();
                $(thisLi).closest(setting.parenClass).find('.city-airport').append(newData).addClass('show-airport');
                $(thisLi).closest(setting.parenClass).find('.city-continent').addClass('dn');

                // 默认选中机场第一个，必须使用setTimeout，因为第一列切换到第二列时有添加过渡效果，导致焦点没办法及时聚焦
                setTimeout(function () {
                  $(thisLi)
                    .closest(setting.parenClass)
                    .find('.city-airport .city-list')
                    .first()
                    .addClass('active')
                    .attr('tabindex', 0)
                    .focus();
                }, 100);
              } else {
                // 进行选中操作
                var city = cityText.split(',')[0]; // 拿到city
                var code1 = cityText.match(/\(.*?\)/g)[0].replace(/[()]/g, ''); // 拿到code，并去除括号
                // 通过code去拼接显示的数据
                var targetGroup1 = that.getGroupByCode(code1); // 找到需要渲染的数据
                var val = city + ', ' + targetGroup1; // 拼接显示的数据
                // 赋值 val data-citycode
                $(thisLi)
                  .closest(setting.parenClass)
                  .find('input')
                  .val(val)
                  .attr('data-citycode', code1.trim().replace(/^\(|\)$/g, ''))
                  .change();
                that.closeComponent(); // 关闭控件
              }
            }

            // 使用键盘下
            if (event.key === 'ArrowDown') {
              if ($(thisLi).index() + 1 < listWrap.find('.city-list').length) {
                $(thisLi).removeClass('active').removeAttr('tabindex');
                // 给后一个增加选中效果
                $(thisLi).next().addClass('active').attr('tabindex', 0).focus();
              } else {
                $(thisLi).removeClass('active').removeAttr('tabindex');
                listWrap.find('.city-list').first().addClass('active').attr('tabindex', 0).focus();
              }
            }

            // 使用键盘上
            if (event.key === 'ArrowUp') {
              if ($(thisLi).index() > 0) {
                $(thisLi).removeClass('active').removeAttr('tabindex');
                // 给上一个增加选中效果
                $(thisLi).prev().addClass('active').attr('tabindex', 0).focus();
              } else {
                $(thisLi).removeClass('active').removeAttr('tabindex');
                listWrap.find('.city-list').last().addClass('active').attr('tabindex', 0).focus();
              }
            }

            // 点击esc按钮，先退出二级，再退出组件，并将焦点置于input上
            if (event.key === 'Escape') {
              var air = $(thisLi).closest(setting.parenClass).find('.show-airport');
              if (air.length) {
                air.removeClass('show-airport');
                air.siblings().removeClass('dn');
                $(thisLi).closest(setting.parenClass).find('.city-continent .city-list-wrap .active').focus(); // 焦点聚焦
              } else {
                that.closeComponent();
                $(inputC).focus(); // 退出时将焦点置于input框上
              }
            }

            // tab按键
            if (event.key === 'Tab') that.closeComponent();
          });
      },
    };
    citySelect.init();
  };
})(jQuery);
