@import '../../less/variables.less';
@import '../../less/mediaMixin.less';

@import './flightInfo.less';
@import './priceSummaryCard.less';
@import './form-tab/form-tab.less';

.module-step {
  display: flex;
  border: 1px solid @gray-2;
  background: @gray-0;

  .screenPad({
     flex-direction: column;
  });

  .screenMobile({
     flex-direction: column;
  });

  &-left {
    flex: 1;
    padding: 20px;
    display: flex;
    align-items: center;

    font-size: 28px;
    font-weight: 500;

    .screenMobile({
      padding:10px 20px;

      font-size: 16px;
      font-weight: 400;
    });
  }

  &-right {
    flex: 1;
    display: flex;
    justify-content: space-evenly;

    .screenPad({
        overflow: hidden;
      });

    .screenMobile({
        overflow: hidden;
      });

    &-step_img {
      // width: 140px;
      width: 100%;
      height: 97px;
      margin-left: -24px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-image: url(../images/flightOptions/step.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;

      &:last-child {
        background-image: url(../images/flightOptions/step-end.png);
      }

      .screenPad({
        height: 70px;

        background-image: url(../images/flightOptions/step-pad.png);

        &:first-child {
          background-image: url(../images/flightOptions/step-start-pad.png);
        }

        &:last-child {
          background-image: url(../images/flightOptions/step-end-pad.png);
        }
      });

      .screenMobile({
        height: 42px;
        margin-left: -12px;

        background-image: url(../images/flightOptions/step-pad.png);

        &:first-child {
          background-image: url(../images/flightOptions/step-start-pad.png);
        }

        &:last-child {
          background-image: url(../images/flightOptions/step-end-pad.png);
        }
      });

      &.active {
        background-image: url(../images/flightOptions/step-select.png);

        &:last-child {
          background-image: url(../images/flightOptions/step-end-select.png);
        }

        .screenPad({
          background-image: url(../images/flightOptions/step-select-pad.png);

          &:first-child {
            background-image: url(../images/flightOptions/step-start-select-pad.png);
          }

          &:last-child {
            background-image: url(../images/flightOptions/step-end-select-pad.png);
          }
        });

        .screenMobile({
          background-image: url(../images/flightOptions/step-select-pad.png);

          &:first-child {
            background-image: url(../images/flightOptions/step-start-select-pad.png);
          }

          &:last-child {
            background-image: url(../images/flightOptions/step-end-select-pad.png);
          }
        });

        .module-step-right-step_text {
          background: @gray-0;
          color: @brand-1;
        }

        .module-step-right-step_text1 {
          color: @gray-0;
        }
      }
    }

    &-step_text {
      margin-left: 10px;
      width: 20px;
      height: 20px;
      border-radius: 9999px;
      background: @gray-1;
      color: @gray-0;
      font-size: 16px;

      display: flex;
      align-items: center;
      justify-content: center;
    }

    &-step_text1 {
      margin-left: 10px;
      margin-top: 6px;
      font-size: 16px;
      color: @sub-2;

      .screenMobile({
        display: none;
      });
    }
  }
}
