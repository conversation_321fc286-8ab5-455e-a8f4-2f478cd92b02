const tabs = [
    document.getElementById('tab1'),
    document.getElementById('tab2'),
    document.getElementById('tab3'),
    document.getElementById('tab4'),
];

const planes = [
    document.getElementById('plane1'),
    document.getElementById('plane2'),
    document.getElementById('plane3'),
    document.getElementById('plane4'),
];


const plane4InnerTabs = [
    document.getElementById('plane4InnerBtn1'),
    document.getElementById('plane4InnerBtn2'),
];

const mobilePlane4InnerTabs = [
    document.getElementById('mobilePlane4InnerBtn1'),
    document.getElementById('mobilePlane4InnerBtn2'),
];

const plane4InnerPlane = [
    document.getElementById('tab1plane'),
    document.getElementById('tab2plane'),
];

const mobilePlane4InnerPlane = [
    document.getElementById('mobileTab1plane'),
    document.getElementById('mobileTab2plane'),
];

const mobileTabs = [
    document.getElementById('mobileTab1'),
    document.getElementById('mobileTab2'),
    document.getElementById('mobileTab3'),
    document.getElementById('mobileTab4'),
  ];

  /**
   * 当前活动中的 【tab】 索引
   * @type {number}
   */
  let currentTabIndex = 1;

  const toggleIndex = (tabIndex) => {
    currentTabIndex = tabIndex;
    tabs.forEach((tab, index) => {
      if (index === tabIndex - 1) {
        tab && tab.classList.add('tab-active');
        planes[index] && planes[index].classList.add('plane-active');
      } else {
        tab && tab.classList.remove('tab-active');
        planes[index] &&  planes[index].classList.remove('plane-active');
      }
    });
  };


  const togglePlane4InnerTab = (index) => {
    plane4InnerTabs.forEach((innerTab, tabIndex) => {
      if (index === tabIndex) {
       innerTab && innerTab.classList.add('inner-tab-item-active');
      } else {
       innerTab && innerTab.classList.remove('inner-tab-item-active');
      }
    });

    plane4InnerPlane.forEach((plane, planeIndex) => {
        if (index === planeIndex) {
            plane && plane.classList.add('plane-4-inner-plane-active');
        } else {
            plane && plane.classList.remove('plane-4-inner-plane-active');
        }
    });
};


toggleIndex(1);
togglePlane4InnerTab(0);


const toggleTab = (tabIndex) => {
    mobileTabs.forEach((tab, index) => {
      if(!tab) return
      const img = tab.getElementsByTagName('i')[0];

      if (tabIndex === index) {
        const isActive = tab.classList.contains('tab-header-active');

        tab.classList.toggle('tab-header-active');
        tab.nextElementSibling.classList.toggle('tab-body-active');

        if (isActive) {
          img.className = 'icon-zh icon-zh-add icon';
        } else {
          img.className = 'icon-zh icon-zh-delete icon';
        }
      } else {
        tab.classList.remove('tab-header-active');
        img.className = 'icon-zh icon-zh-add icon';
        tab.nextElementSibling.classList.remove('tab-body-active');
      }
    });
};
const toggleMobilePlane4InnerTab = (tabIndex) => {

    mobilePlane4InnerTabs.forEach((innerTab, index) => {
        if (index === tabIndex) {
            innerTab && innerTab.classList.add('inner-tab-item-active');
        } else {
            innerTab && innerTab.classList.remove('inner-tab-item-active');
        }
    });

    mobilePlane4InnerPlane.forEach((plane, index) => {
        if (index === tabIndex) {
           plane && plane.classList.add('plane-4-inner-plane-active');
        } else {
            plane && plane.classList.remove('plane-4-inner-plane-active');
        }
    });
};

toggleTab(0);
toggleMobilePlane4InnerTab(0);

/**
 * @param {HTMLInputElement} dom
 */
const openDropdown = (dom) => {
    // dom是input元素，可以通过dom.idn拿到自定义的id，然后根据id来const不同的optionList，用switch或者if都可以。
    // optionList可以直接放在config.js里方便做国际化
    const optionList = [
        {id: 1, label: "hello world1", value: 1},
        {id: 2, label: "hello world2", value: 2},
        {id: 3, label: "hello world3", value: 3},
        {id: 4, label: "hello world4", value: 4},
    ];

    dom.openDropdownSelect(optionList, {
        labelFiled: 'label',
        valueFiled: 'id',
        defaultValue: 1,
    });
};


$(function () {
    // seat check-in 点击事件
    $('.pc-form-tab-wrap .plane .tab, .mobile-from-tab-wrap .book-seat-check .tab, .modal-content-flight .book-seat-check .tab').on('click', function () {
        var $tabTarget = $(this).closest('.tab-target');
        var $index = $(this).index();
        var siblingsLen = $(this).siblings().length; // 通过长度判断是航班的点击事件，还是座位的点击事件

        $(this).siblings().removeClass('active');
        $(this).addClass('active');
        // $tabTarget.find('.form-items').removeClass('active').eq($index).addClass('active');
        // tab的选中效果
        if ($index !== 2 && siblingsLen > 1) {
            $tabTarget.find('.form-items').removeClass('active').eq(0).addClass('active');
        } else if (siblingsLen > 1){
            $tabTarget.find('.form-items').removeClass('active').eq(1).addClass('active');
        } else {
            $tabTarget.find('.form-items').removeClass('active').eq($index).addClass('active');
        }
        // 去程
        const $departure = $('.calendar-group.round-date').find('.depart-input');
        const departureVal = $departure.eq(0).val();
        const departureValMobile = $departure.eq(1).val();
        // 返程
        const $return = $('.calendar-group.round-date').find('.return-input');
        const returnVal = $return.eq(0).val();
        const returnValMobile = $return.eq(1).val();

        // 是否需要置灰回程
        if ($index === 0) {
            $return.closest('.input-group').addClass('disabled');
            $return.attr('disabled', 'disabled');
            $return.addClass('disable');
        }
        if ($index === 1) {
            $return.closest('.input-group').removeClass('disabled');
            $return.removeAttr('disabled');
            $return.removeClass('disable');

            // pc 切换到往返时 去程大于回程
            if (new Date(departureVal) >= new Date(returnVal)) {
              var newDate = new Date(new Date(departureVal).getTime() + 2 * 24 * 60 * 60 * 1000);
              var year = newDate.getFullYear();
              var month = (newDate.getMonth() + 1).toString().padStart(2, '0');
              var day = newDate.getDate().toString().padStart(2, '0');
              var formattedDate = `${year}-${month}-${day}`;
              $return.eq(0).val(formattedDate).attr("data-date", formattedDate);
            }

            // mobile 切换到往返时 去程大于回程
            if (new Date(departureValMobile) >= new Date(returnValMobile)) {
              var newDate = new Date(new Date(departureValMobile).getTime() + 2 * 24 * 60 * 60 * 1000);
              var year = newDate.getFullYear();
              var month = (newDate.getMonth() + 1).toString().padStart(2, '0');
              var day = newDate.getDate().toString().padStart(2, '0');
              var formattedDate = `${year}-${month}-${day}`;
              $return.eq(1).val(formattedDate).attr("data-date", formattedDate);
            }
        }
    });

    // 添加多目的航段
    $(".add-wrap .add-trip").not('.disable-add').on('click', function () {
        var $flightWrapper = $(this).closest('.multi-group').find('.flight-wrapper');
        // 集合
        var flgithList = $flightWrapper.children();
        // 集合的长度
        var flightNums = flgithList.length;
        // 最后一个子元素的
        var lastCalendarVal = flgithList.last().find(".calendar-group .input-group>input.multi-calendar").val();
        var lastDate = new Date(lastCalendarVal);
        var year_last = lastDate.getFullYear();
        var month_last = (lastDate.getMonth() + 1).toString().padStart(2, '0');
        var day_last = lastDate.getDate().toString().padStart(2, '0');
        var formattedDate_last = `${year_last}-${month_last}-${day_last}`;
        // 新增的航段 日期继续往后+2天
        var newDate = new Date(new Date(lastCalendarVal).getTime() + 2 * 24 * 60 * 60 * 1000);
        var year = newDate.getFullYear();
        var month = (newDate.getMonth() + 1).toString().padStart(2, '0');
        var day = newDate.getDate().toString().padStart(2, '0');
        var formattedDate = `${year}-${month}-${day}`;
        if (flightNums < 6) {
            var newFlight = $flightWrapper.find('.num-item').eq(0).clone(true); // clone
            newFlight.find('.from input,.to input').val(''); // 清除数据
            newFlight.find('.flight-id').text(flightNums + 1);
            newFlight.find('.calendar-group .input-group>input.multi-calendar').val(formattedDate)
              .attr("data-date", formattedDate)
              .attr("data-disable-last-date",formattedDate_last);
            $flightWrapper.append(newFlight);
            flightNums = $flightWrapper.children().length;
            if (flightNums >= 3) {
                $('.delete-flight-btn').show();
            }
        }
    });

    // 删除航段
    $('.flight-wrapper').on('click', '.delete-flight-btn', function () {
        var $multiGroup = $(this).closest('.multi-group');
        var wrapper = $multiGroup.find('.flight-wrapper');
        $(this).closest('.num-item').remove();
        var length = wrapper.children().length;
        if (length <= 2) {
            $('.delete-flight-btn').hide();
        }
        if (length < 6) {
            $('.add-flight-btn').removeClass('disable-add');
        }
        // 删除时自动标号
        autoIndex($multiGroup.is('.pc'));
    });

    function autoIndex(len) {
        // PC 还是移动端的点击事件
        if (len) {
            $('.pc-form-tab-wrap .flight-wrapper .num-item').each(function (index, _) {
                var flight_id = $(this).find('.flight-id');
                var tindex = index + 1;
                flight_id.text(tindex);
            });
        } else {
            $('.mobile-from-tab-wrap .flight-wrapper .num-item').each(function (index, _) {
                var flight_id = $(this).find('.flight-id');
                var tindex = index + 1;
                flight_id.text(tindex);
            });
        }
    }

    // 多目的地选择日历 多日历联动
    $('.multi-calendar').on('change', function (event) {
      var $flightWrapper = $(this).closest('.multi-group').find('.flight-wrapper');

      // 找到最近的 num-item 父元素
      var numItem = $(this).closest(".num-item");
      // 找到 num-item 元素在其兄弟元素中的索引位置
      var numItemIndex = numItem.index();

      var multiCalendars = $flightWrapper.find('.multi-calendar')

      // 将字符串日期转换为 Date 对象
      let date = new Date(event.target.value);

      // 找出index比现在的日历大的 并修改他们的值。
      multiCalendars.each((index, item) => {
        if (index > numItemIndex) {
          if (date >= new Date(item.value)) {
            // 增加两天
            date.setDate(date.getDate() + 2);
            // 重新格式化为 yyyy-mm-dd
            let newYear = date.getFullYear();
            let newMonth = (date.getMonth() + 1).toString().padStart(2, '0');
            let newDay = date.getDate().toString().padStart(2, '0');
            const formattedDate = `${newYear}-${newMonth}-${newDay}`;
            $(item).val(formattedDate).attr("data-date",formattedDate)
          }
        }
        if (index >0 && index > numItemIndex) {
          $(item).attr("data-disable-last-date",multiCalendars.eq(index-1).val())
        }
      })
    })

    // 出发、回程交换
    $('.change-wrap').on('click', '.change', function () {
        var winWidth = $(window).width();
        var $fromVal = $(this).parent().siblings('.from').find('.text');
        var $toVal = $(this).parent().siblings('.to').find('.text');

        // 移动端事件
        if (winWidth < 768) {
            $fromVal = $($(this).closest('.row').find('.text')[0]);
            $toVal = $($(this).closest('.row').find('.text')[1]);
        }

        // 获取val值跟城市编码
        var fromValue = $fromVal.val();
        var toValue = $toVal.val();
        var cityCodeFrom = $fromVal.attr('data-citycode');
        var cityCodeTo = $toVal.attr('data-citycode');

        // 交换val值、code
        if (fromValue || toValue) {
            $fromVal.val(toValue);
            $toVal.val(fromValue);
            $fromVal.attr('data-citycode', cityCodeTo);
            $toVal.attr('data-citycode', cityCodeFrom);
        }
        $(this).addClass("rotate");
        const rotateTimeout = setTimeout(() => {
          $(this).removeClass("rotate");
          clearTimeout(rotateTimeout)
        }, 1000);

    }).on('keydown', '.change', function (event) {
        var winWidth = $(window).width();
        var $fromVal = $(this).parent().siblings('.from').find('.text');
        var $toVal = $(this).parent().siblings('.to').find('.text');

        // 移动端事件
        if (winWidth < 768) {
            $fromVal = $($(this).closest('.row').find('.text')[0]);
            $toVal = $($(this).closest('.row').find('.text')[1]);
        }
        // 获取val值跟城市编码
        var fromValue = $fromVal.val();
        var toValue = $toVal.val();
        var cityCodeFrom = $fromVal.attr('data-citycode');
        var cityCodeTo = $toVal.attr('data-citycode');
        // 交换val、code
        if ((fromValue || toValue) && event.key === 'Enter') {
            $fromVal.val(toValue);
            $toVal.val(fromValue);
            $fromVal.attr('data-citycode', cityCodeTo);
            $toVal.attr('data-citycode', cityCodeFrom);
        }
    });

    $('.select-text').on('focus', function () {
        var opt = [
            {id: 1, label: "Economy class", value: 1},
            {id: 2, label: "First class/Business class", value: 2},
        ];

        this.openDropdownSelect(opt, {
            labelFiled: 'label',
            valueFiled: 'id',
            defaultValue: 1,
        });
    });

    // 城市 校验逻辑
    $('input.city-component').on('change', function () {
      var winWidth = $(window).width();
      // 去掉错误提示
      $(this).next('.error-tips').remove();
      // 去掉错误红框
      $(this).closest('.input-group').removeClass('error');
      // 去程的input
      var $fromVal = $(this).closest('.row').find('.from').find('.text');
      // 回程的input
      var $toVal = $(this).closest('.row').find('.to').find('.text');

      // 移动端事件
      if (winWidth < 768) {
        $fromVal = $($(this).closest('.row').find('.text')[0]);
        $toVal = $($(this).closest('.row').find('.text')[1]);
      }
      // 出发input的值
      var fromValue = $fromVal.val();
      // 到达input的值
      var toValue = $toVal.val();

      // 去程回程选择相同城市
      if (fromValue === toValue) {
        $(this).closest('.input-group').addClass('error');
        const erorrDom = `<div class="error-tips">
                          <span class="icon-zh icon-zh-error"></span>
                          <span class="error-content">出发到达城市不能相同</span>
                        </div>`
        $(this).parent().append(erorrDom);
      }
    })
});


const replacementValue = (inputId1, inputId2) => {
    const input1 = document.getElementById(inputId1);
    const input2 = document.getElementById(inputId2);

    let temp = input1.value;
    input1.value = input2.value;
    input2.value = temp;
  $(this).addClass("rotate");
  const rotateTimeout = setTimeout(() => {
    $(this).removeClass("rotate");
    clearTimeout(rotateTimeout)
  }, 1000);
};

$(document).ready(function () {
  // flight - multi-city 第二段航段的日期为当天+2
  var currentDate = new Date();
  var newDate = new Date(currentDate.getTime() + 2 * 24 * 60 * 60 * 1000);
  var year = newDate.getFullYear();
  var month = (newDate.getMonth() + 1).toString().padStart(2, '0');
  var day = newDate.getDate().toString().padStart(2, '0');
  var formattedDate = `${year}-${month}-${day}`;
  $("#single-date-sg2").val(formattedDate).attr("data-date", formattedDate);
  $("#single-date-mobile-sg2").val(formattedDate).attr("data-date", formattedDate);

  // my booking和flight status日历默认值为空
  $(".calendar-input-disabled-before").val('');
})

