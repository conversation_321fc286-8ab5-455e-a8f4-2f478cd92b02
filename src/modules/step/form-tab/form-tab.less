@import '../../../less/variables.less';
@import '../../../less/mediaMixin.less';

.dropdown-icon {
  pointer-events: none;
}

.w-full {
  width: 100%;
}

.book-seat-check {
  .form-items {
    display: none;

    &.active {
      display: block;
    }
  }

  .form-tab {
    margin-bottom: 1.5rem;
    padding-left: 0.5rem;
  }

  .form-seat-flex {
    display: grid;
    gap: 2rem 1.5rem;
    margin-bottom: 1rem;
    grid-template-columns: repeat(3, 1fr);
  }

  .check-read {
    display: flex;
    color: #4f3d1e;
    font-size: 0.8rem;
    align-items: center;
    margin-bottom: 1rem;
  }

  .warm-tips {
    padding: 0.5rem 1rem;
    background-color: #fcf1d2;
    color: #4f3d1e;
    font-size: 0.7rem;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(238, 183, 28, 0.6);
    .title {
      margin-bottom: 0.5rem;
      color: #101010;
      font-size: 0.8rem;
    }
  }

  .btn-box {
    .btn {
      background-color: @picker-color-010;
      border-radius: 0.4rem;
      min-width: 11.5rem;
      padding: 0.7rem 0.4rem;
      font-size: 1rem;
      color: @color-white;
      text-align: center;

      &:hover,
      &:focus {
        background-color: #942531;
      }

      &.disabled {
        color: #b4aba4;
        background-color: #dbd7d4;
      }
    }
  }

  .tab {
    float: left;
    font-size: 0.8rem;
    color: #4f3d1e;
    cursor: pointer;
    margin-right: 2rem;
    padding-bottom: 0.5rem;
    border-bottom: 3px solid transparent;

    &:nth-child(3) {
      margin-right: 0;
    }

    &.active {
      color: @gray-101010;
      border-bottom: 3px solid #cc0100;
    }
  }
}

// flights模块
.book-flights {
  &.modal {
    .mb-box {
      margin-bottom: 2rem;
    }

    .block {
      display: block;
    }

    .none {
      display: none;
    }

    .pr {
      padding-right: 1.9rem;
    }

    .w30 {
      width: auto;
      margin-right: 0;
    }

    .pad-flex {
      display: flex;
      gap: 3.8rem;

      & > div {
        flex: 1;
      }
    }
  }

  .seat-flex {
    display: flex;

    .input-wrap {
      flex: 1;

      &.change-wrap {
        flex: none;
        width: 3.8rem;
        // margin: 0 1.5rem;
        .change {
          display: inline-block;
          transition: transform 0.5s ease-in-out;
          &.rotate {
            animation: spin 1s linear;
          }
        }
        @keyframes spin {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
      }
    }
  }

  .pc-limit {
    width: 92%;
  }

  .form-tab {
    margin-bottom: 1.5rem;
    padding-left: 0.5rem;

    .tips {
      display: inline-block;
      margin-left: 0.5rem;

      .icon-zh {
        vertical-align: top;
      }
    }
  }

  .tab {
    margin-right: 2rem;
  }

  .change-wrap {
    text-align: center;
    line-height: 3.25rem;
  }

  .input-wrap {
    .change {
      color: @picker-color-010;
      font-size: 28px;
      cursor: pointer;
      display: inline-block;
      transition: transform 0.5s ease-in-out;
      &.rotate {
        animation: spin 1s linear;
      }
    }
    @keyframes spin {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
  }

  .btn-flex {
    display: flex;
    flex-direction: column-reverse;
  }

  .form-wrap-col {
    .row {
      margin-bottom: 2rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .pc-pr-rest {
    padding-right: 0.4rem;
  }

  .pc-pl-rest {
    padding-left: 0.4rem;
  }

  .w30 {
    width: 31%;
    margin-right: 3rem;
    padding: 0;
  }

  .add-wrap {
    .button {
      display: inline-block;
      cursor: pointer;
      min-width: 12rem;
      text-align: center;
      border: 1px solid @picker-color-010;
      color: @picker-color-010;
      font-size: 1rem;
      padding: 0.7rem 0.5rem;
      border-radius: 0.4rem;

      .icon-zh {
        font-size: 0.6rem;
        margin-right: 0.3rem;
        vertical-align: middle;
      }

      &:hover,
      &:focus {
        border-color: #942531;
        color: #942531;
      }

      &.disabled {
        color: #b4aba4;
        border-color: #cccccc;
      }
    }
  }

  .flight-wrapper {
    margin-bottom: 2rem;
  }

  .num-item {
    position: relative;

    .flight-id {
      position: absolute;
      color: @color-white;
      background-color: @picker-bg-color;
      width: 1.1rem;
      height: 1.1rem;
      line-height: 1.1rem;
      font-size: 0.6rem;
      border-radius: 50%;
      left: -1rem;
      padding: 0;
      text-align: center;
      top: 2px;
    }

    .delete-flight-btn {
      position: absolute;
      width: 0.8rem;
      font-size: 0.6rem;
      color: @picker-color-010;
      cursor: pointer;
      top: 1.6rem;
      right: -1rem;
      padding: 0;
      display: none;
    }
  }

  .multi-group {
    padding-left: 1.5rem;
  }

  .input-passenger {
    .text {
      padding-right: 24px !important;
    }
  }
}

.btn-box {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

// 旅客下拉
.passenger-count-wrap {
  display: none;
  position: absolute;
  left: 0;
  z-index: 59;
  top: 100%;
  box-shadow: 1px 1px 12px 0 rgba(0, 0, 0, 0.2);
  background: @color-white;
  border-radius: 0.2rem;
  width: 100%;
  min-width: 274px;
  border: 1px solid @gray-lighter;

  .passenger-count-list {
    margin: 0 0.5rem;
  }

  .passenger-type {
    display: flex;
    justify-content: space-around;
    margin-bottom: 1.5rem;

    .type {
      color: #4f3d1e;
      cursor: pointer;
      padding: 1rem 0 0 0;

      &.active {
        color: @gray-101010;
        position: relative;
        font-weight: 500;

        &::before {
          content: '';
          position: absolute;
          bottom: -0.7rem;
          left: 0;
          width: 100%;
          height: 0.2rem;
          background: @picker-color-010;
        }
      }
    }
  }
}

.passenger-count-list {
  display: none;

  li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    border: 1px solid transparent;
    border-radius: 0.1rem;
    height: 3.5rem;

    &:hover {
      border-color: @picker-bg-color;
    }

    &:last-child:hover {
      border-color: transparent;
    }
  }

  .number {
    font-size: 1rem;
    display: inline-block;
    width: 1.6rem;
    text-align: center;
  }

  .name {
    font-size: 1rem;
    color: #101010;
    position: relative;
    padding-left: 1.5rem;
    width: 70%;

    .icon-zh {
      position: absolute;
      color: #dbd7d4;
      left: 0;
      font-size: 1.2rem;
      top: 0.5rem;
    }

    .limit {
      display: block;
      margin-top: 5px;
      font-size: 0.7rem;
      color: #4f3d1e;
    }
  }

  .count-tools {
    display: flex;
    align-items: center;

    a[role='button'] {
      .icon-zh {
        font-size: 0.9rem;
        color: @picker-color-010;
      }

      &.disabled {
        .icon-zh {
          color: #dbd7d4;
        }
      }
    }
  }

  &.show-this {
    display: block;
  }

  .btn-wrap {
    justify-content: flex-end;
    a {
      display: inline-block;
      min-width: 6rem;
      border-radius: 0.4rem;
      text-align: center;
      padding: 0.5rem 0;
      margin-left: 1rem;
    }

    .btn-single {
      border: 1px solid @primary-color;
      color: @primary-color;

      &:hover,
      &:focus {
        border: 1px solid #942531;
        color: #942531;
      }
    }

    .btn-default {
      background-color: @primary-color;
      color: @color-white;

      &:hover,
      &:focus {
        background-color: #942531;
      }
    }
  }

  .btn-wrap {
    padding: 0.5rem 0;
    a {
      margin-left: 0.5rem;
    }
  }

  // 其他乘客类型选择样式
  &.others {
    .name {
      .icon-zh {
        top: -0.2rem;
      }
    }
  }
}

@media screen and (min-width: 768px) {
  .mobile-from-tab-wrap {
    display: none;
  }

  .pc-form-tab-wrap {
    border-width: 0 1px 1px 1px;
    border-style: solid;
    border-color: #cccccc;
    background-color: #ffffff;

    &.modal,
    &.page {
      border-width: 0;
    }

    .tabs-wrap {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 0.15rem;

      .tab {
        flex: 1;
        flex-shrink: 0;
        height: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;

        font-size: 1rem;
        color: #ffffff;
        background: #554e49;
        cursor: pointer;
        transition: 0.2s;
        user-select: none;
        text-align: left;
        padding: 0 0.5rem;

        &-active {
          background: #ffffff;
          color: #101010;
        }
      }
    }

    .plane {
      display: none;
      width: 100%;
      padding: 1.5rem;
      box-sizing: border-box;
      min-height: 10rem;

      &-active {
        display: block;
      }

      .submit-btn {
        width: 100%;
        //height        : 2.45rem;
        //line-height   : 2.45rem;
        //text-align    : center;
        //font-size     : 1rem;
        //color         : #ffffff;
        //border-radius : 0.4rem;
        //background    : #CC0100;
        //cursor        : pointer;
        margin-top: 1.5rem;
      }

      .book-plane-inner {
        .form-items-wrap {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 2rem 3rem;
        }
      }

      .status-plane-inner {
        .inner-tab-wrap {
          display: flex;
          align-items: center;
          padding-left: 0.5rem;
          font-size: 0.8rem;

          .inner-tab-item {
            margin-right: 2.35rem;
            cursor: pointer;
            color: #4f3d1e;
            transition: 0.2s;
            font-size: 0.8rem;

            &-active {
              position: relative;
              color: #101010;

              &::after {
                content: '';
                position: absolute;
                bottom: -0.8rem;
                left: 0;
                width: 100%;
                height: 0.2rem;
                background: #cc0100;
              }
            }
          }
        }

        .plane-4-inner-plane {
          display: none;

          .change {
            color: @picker-color-010;
            font-size: 28px;
            cursor: pointer;
          }

          &-active {
            display: block;
          }
        }

        .form-items-wrap {
          margin-top: 2rem;
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          justify-content: space-between;
          row-gap: 1.5rem;

          &.row {
            margin-top: 0;
          }

          .icon-repeatedly {
            margin: 0 0.6rem;
            font-size: 1.4rem;
            color: @picker-color-010;
            cursor: pointer;
          }

          .input-group {
            width: 45%;

            .label-wrap {
              left: 0.5rem;
            }

            &-btn {
              display: flex;
              align-items: center;
              justify-content: flex-end;

              width: 100%;
            }
          }
        }
      }
    }
  }
}

@media screen and (min-width: 1025px) {
  .pc-form-tab-wrap {
    border-width: 0 1px 1px 1px;
    border-style: solid;
    border-color: #cccccc;

    .plane {
      padding: 2rem;

      .submit-btn {
        margin-top: 2rem;
        width: 11.5rem !important;
      }

      .book-plane-inner {
        .form-items-wrap {
          gap: 2rem 1.5rem;
        }
      }
    }
  }

  .book-plane-inner {
    .form-items-wrap {
      grid-template-columns: repeat(3, 1fr) !important;
    }
  }

  .form-items-wrap {
    &-start {
      justify-content: flex-start !important;
      column-gap: 4.4rem;

      .input-group {
        width: 30% !important;
      }
    }

    &.row {
      justify-content: flex-start !important;
    }

    &-icon {
      .input-group {
        width: 30% !important;
      }
    }
  }
}

@media screen and (max-width: @screen-lg-max) {
  .book-flights {
    .padding-l-1 {
      padding-left: 0.5rem;
    }
  }
}

@media screen and (max-width: 767px) {
  .pc-form-tab-wrap {
    display: none;
  }

  .mobile-from-tab-wrap {
    display: block;

    .input-group {
      margin-bottom: 0.5rem;
    }

    .submit-btn {
      width: 100%;
      //height        : 2.45rem;
      //line-height   : 2.45rem;
      //text-align    : center;
      //font-size     : 1rem;
      //color         : #ffffff;
      //border-radius : 0.4rem;
      //background    : #CC0100;
      //cursor        : pointer;
    }

    .tab {
      display: grid;
      font-size: 1rem;
      background-color: #ffffff;
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }

      .tab-header {
        display: grid;
        grid-template-areas: '. text icon';
        grid-template-rows: 2.5rem;
        justify-items: center;
        align-items: center;
        background-color: #554e49;
        color: #ffffff;
        font-size: 0.8rem;
        padding: 0 0.95rem;
        transition: 0.2s;

        &-active {
          background-color: #ffffff;
          color: #101010;
          border: 1px solid @gray-lighter; // 增加移动端border效果，勿删
        }

        .tab-header-text {
          grid-area: text;
        }

        .icon {
          grid-area: icon;
          justify-self: end;
        }
      }

      .tab-body {
        display: none;
        min-height: 5rem;
        padding: 1rem;
        box-sizing: border-box;
        border: 1px solid @gray-lighter; // 增加移动端border效果，勿删

        &-active {
          display: block;
        }

        .inner-tab-wrap {
          display: flex;
          align-items: center;

          font-size: 0.8rem;

          .inner-tab-item {
            margin-right: 2.35rem;
            cursor: pointer;
            color: #4f3d1e;
            transition: 0.2s;

            &-active {
              position: relative;
              color: #101010;

              &::after {
                content: '';
                position: absolute;
                bottom: -0.8rem;
                left: 0;
                width: 100%;
                height: 0.2rem;
                background: #cc0100;
              }
            }
          }
        }

        .plane-4-inner-plane {
          display: none;
          margin-top: 1.2rem;

          .input-group-short {
            width: calc(100% - 2.2rem);
          }

          .change {
            color: @picker-color-010;
            font-size: 28px;
            cursor: pointer;
          }

          &-active {
            display: block;
          }

          .cb-wrap {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .cb-left {
              flex: 1;
              width: 80%;
            }

            .icon-repeatedly {
              flex-shrink: 0;
              width: 1.4rem;
              height: 1.4rem;
              object-fit: cover;
              margin-left: 0.8rem;
              transform: translateY(-1rem);
            }
          }
        }
      }
    }
  }
}

@media (max-width: @screen-md-max) {
  .book-seat-check {
    .form-seat-flex {
      gap: 2rem 3rem;
      grid-template-columns: repeat(2, 1fr);
    }

    .btn-box {
      text-align: initial;

      .btn {
        width: 100%;
      }
    }
  }

  .book-flights {
    .padding-l-1 {
      padding-left: 0.5rem;
    }

    .mb-box {
      margin-bottom: 2rem;
    }

    .pc-limit {
      width: auto;
    }

    .w30 {
      width: auto;
      margin-right: 0;
    }

    .pc-pr-rest {
      padding-right: 0;
    }

    .pc-pl-rest {
      padding-left: 0;
    }

    .pr {
      padding-right: 1.9rem;
    }

    .pad-flex {
      display: flex;
      gap: 3.8rem;

      & > div {
        flex: 1;
      }
    }

    // .flex-r {
    //   gap: 3.4rem;
    // }

    .num-item {
      .delete-flight-btn {
        right: -0.4rem;
      }
    }

    .pad-rest-ml {
      margin-left: -2rem;
    }
  }

  .passenger-count-wrap {
    .passenger-type {
      justify-content: inherit;
      gap: 1.2rem;
      margin-left: 1rem;
      margin-bottom: 1.2rem;
    }
  }

  .passenger-count-list {
    li {
      height: 3rem;
    }
    .name {
      font-size: 0.8rem;
      width: 60%;
      .icon-zh {
        font-size: 0.9rem;
      }
    }
    &.others {
      .name {
        .icon-zh {
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }
}

@media (max-width: @screen-xs-max) {
  .book-seat-check {
    .check-read {
      align-items: flex-start;
      column-gap: 0.4rem;
    }

    .form-tab {
      display: flex;
      align-items: baseline;
      margin-bottom: 1rem;

      .tab {
        font-size: 0.8rem;
        margin: 0 1.5rem 0 0;

        &:first-child {
          width: 38%;
        }

        &:last-child {
          margin-right: 0;
          display: flex;
          gap: 0.5rem;
        }
      }
    }

    .form-seat-flex {
      display: block;

      .input-wrap {
        width: 100%;
      }
    }

    .btn-box {
      margin-top: 0.5rem;
    }
  }

  .book-flights {
    padding: 0;

    .form-wrap-col {
      .row {
        margin-bottom: 0;
      }

      .col-mobile-1 {
        padding: 0;
      }
    }

    .multi-group {
      .form-wrap-col {
        .row {
          margin-bottom: 1rem;
        }
      }

      .add-wrap {
        margin-top: -0.5rem;
        margin-bottom: 1rem;
      }

      .btn-box {
        margin: 1rem 0 -0.5rem 0;
      }
    }

    .multi-group {
      padding: 0 0.5rem 0 1.5rem;

      .change-wrap {
        left: 0.4rem;
      }
    }

    .flight-wrapper {
      margin-bottom: 0;
    }

    .change-wrap {
      position: absolute;
      top: 1.4rem;
      left: 50%;
      transform: translateX(-50%);
    }

    .num-item {
      .delete-flight-btn {
        top: 1.6rem;
        left: 0.8rem;
      }
    }

    .form-tab {
      margin-right: -1.5rem;

      .tips {
        .icon-zh {
          vertical-align: middle;
        }
      }

      .tab {
        margin: 0 1.5rem 0 0;

        &:first-child {
          width: auto;
        }

        &:nth-child(3) {
          margin-right: 0;
        }
      }
    }

    .reset-btn-width {
      margin: 0 -0.5rem 0.5rem -1.5rem;
    }

    .add-wrap {
      .button {
        width: 100%;
      }
    }
  }
}
