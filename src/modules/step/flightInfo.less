@import '../../less/variables.less';
@import '../../less/mediaMixin.less';

.step-flight_info-container {
  width: 100%;

  .step-flight_info {
    width: 100%;
    display: flex;
    align-items: end;
    justify-content: space-between;

    .screenMobile({
        flex-direction: column;
        align-items: start;
      });

    &-top {
      display: flex;
      align-items: center;
    }

    &-tag {
      margin-top: 10px;
      border-radius: 8px;
      background: @sub-1;
      margin-right: 10px;
      padding: 0 10px;

      color: @sub-4;
      font-size: 14px;

      .screenMobile({
          font-size: 12px;
          padding: 0 ;
        });
    }

    &-text {
      display: flex;
      align-items: center;

      color: @gray-5;
      font-size: 20px;

      .screenMobile({
          font-size: 16px;
        });
    }

    &-way_icon {
      margin: 0 10px;
    }

    &-bottom {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
    }

    &-person {
      color: @sub-4;
      font-size: 14px;
      margin-top: 10px;

      .screenMobile({
          font-size: 12px;
        });
    }

    .wrap-button {
      .screenMobile({
          margin-top: 10px;
          width: 100%;
          display: flex;
          justify-content: end;
        });
    }
  }

  .sz-modal {
    .modal-content-flight {
      display: block;
      max-height: 60vh;
      overflow-y: auto;
    }
  }

  .modal-content-flight {
    display: none;
  }
}
