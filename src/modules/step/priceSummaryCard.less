@import '../../less/variables.less';
@import '../../less/mediaMixin.less';

.shopping-cart {
  width: 100%;
  background: @gray-0;
  // box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.2); /* token: effect_57:2622 */
  border: 1px solid @gray-2; /* token: 中性色/gray-2 */
  border-top: none;
  display: none;

  &.always {
    display: block;
  }

  .screenPad({
    display: block;
  });

  .screenMobile({
    display: block;
  });

  /* 当cart-body展开时，添加下方圆角 */
  &.expanded {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;

    overflow: hidden;

    .cart-header {
      border-bottom: 1px solid @gray-2; /* token: 中性色/gray-2 */
    }
  }

  /* 购物车头部 */
  .cart-header {
    background: linear-gradient(180deg, #f6f6f6 0%, rgba(255, 255, 255, 0) 100%), @gray-0; /* token: paint_57:2791 */
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;

    // box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.08); /* token: effect_57:2794 */

    .cart-header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      margin-right: 30px;

      .screenMobile({
        margin-right: 10px;
      });

      .total-price-label {
        font-size: 24px; /* token: 24px/24px-默认 */
        font-weight: 400;
        color: @gray-3; /* token: 中性色/gray-3 */
        line-height: auto;

        .screenMobile({
          font-size: 16px; 
        });
      }

      .total-price-amount {
        display: flex;
        align-items: center;
        gap: 8px;

        .currency {
          font-size: 16px; /* token: 16px/16px-默认 */
          font-weight: 400;
          color: @sub-4; /* token: 辅色/sub-4 */
          line-height: auto;

          .screenMobile({
            font-size: 12px; 
          });
        }

        .amount {
          font-size: 28px; /* token: 28px/28px-粗 */
          font-weight: 500;
          color: @brand-1; /* token: 品牌色/brand-1 */
          line-height: auto;

          .screenMobile({
            font-size: 16px; 
          });
        }
      }
    }

    .view-details {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;

      .view-details-text {
        font-size: 20px; /* token: 20px/20px-默认 */
        font-weight: 400;
        color: @orange-3; /* token: 状态色/orange-3 */
        line-height: auto;

        .screenMobile({
          font-size: 14px;
        });
      }

      .icon-arrow-down {
        transform: rotate(-180deg);
        transition: transform 0.3s ease;
      }
    }

    /* 装饰性图案 */
    .decorative-pattern {
      position: absolute;
      top: 20px;
      right: 2px;
      width: 61px;
      height: 64px;
      pointer-events: none;
    }
  }

  /* 购物车主体 */
  .cart-body {
    width: 100%;
    background: @gray-0; /* token: 中性色/gray-0 */
    padding: 20px;
    overflow: hidden;
    transition: max-height 0.3s ease, opacity 0.3s ease;

    &.collapsed {
      max-height: 0;
      padding: 0 20px;
      opacity: 0;
    }

    &.expanded {
      max-height: 1000px;
      opacity: 1;
    }

    .passengers-container {
      display: flex;
      flex-wrap: wrap;
      gap: 40px;

      .screenMobile({
        width: 100%;
        height: auto;
        flex-direction: column;
        gap: 20px;
      });

      .passenger-section {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 10px;

        .screenMobile({
          width: 100%;
          height: auto;
        });

        /* 乘客标题 */
        .passenger-header {
          padding: 0 20px;
          display: flex;
          align-items: center;

          .passenger-name {
            font-size: 20px; /* token: 20px/20px-粗 */
            font-weight: 500;
            color: @sub-4; /* token: 辅色/sub-4 */
            line-height: auto;

            .screenMobile({
              font-size: 16px;
            });
          }

          .screenMobile({
            padding: 0 10px;
          });
        }

        /* 票价行 */
        .fare-row {
          height: 23px;
          padding: 0 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .screenMobile({
            padding: 0 10px;
          });

          .fare-label {
            display: flex;
            align-items: center;
            gap: 6px;

            span {
              font-size: 16px; /* token: 16px/16px-默认 */
              font-weight: 400;
              color: @gray-3; /* token: 中性色/gray-3 */
              line-height: auto;
            }
          }

          .fare-amount {
            font-size: 16px; /* token: 16px/16px-默认 */
            font-weight: 400;
            color: @sub-4; /* token: 辅色/sub-4 */
            line-height: auto;
            text-align: right;

            .screenMobile({
              font-size: 12px;
            });
          }
        }

        /* 税费行 */
        .taxes-row {
          height: 23px;
          padding: 0 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .screenMobile({
            padding: 0 10px;
          });

          .taxes-label {
            display: flex;
            align-items: center;
            gap: 10px;

            span {
              font-size: 16px; /* token: 16px/16px-默认 */
              font-weight: 400;
              color: @sub-4; /* token: 辅色/sub-4 */
              line-height: auto;
            }

            .question-icon {
              width: 16px;
              height: 16px;
              font-size: 0;
              cursor: pointer;

              svg {
                width: 100%;
                height: 100%;
              }

              &:hover {
                opacity: 0.8;
              }
            }
          }

          .taxes-amount {
            font-size: 16px; /* token: 16px/16px-默认 */
            font-weight: 400;
            color: @sub-4; /* token: 辅色/sub-4 */
            line-height: auto;
            text-align: right;

            .screenMobile({
              font-size: 12px;
            });
          }
        }

        /* 乘客小计 */
        .passenger-total {
          height: 29px;
          padding: 0 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 10px;

          .screenMobile({
            padding: 0 10px;
          });

          .total-label {
            font-size: 20px; /* token: 20px/20px-粗 */
            font-weight: 500;
            color: @sub-4; /* token: 辅色/sub-4 */
            line-height: auto;

            .screenMobile({
              font-size: 16px;
            });
          }

          .total-amount {
            font-size: 20px; /* token: 20px/20px-粗 */
            font-weight: 500;
            color: @sub-4; /* token: 辅色/sub-4 */
            line-height: auto;

            .screenMobile({
              font-size: 16px;
            });
          }
        }
      }
    }
  }
}
