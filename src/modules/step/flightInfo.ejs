<%
  const _routeType = typeof routeType !== 'undefined' ? routeType: 1; // 1:单程 2:往返 3:多段
%>

<div class="step-flight_info-container">
  <div class="step-flight_info">
    <div>
      <div class="step-flight_info-top">
        <span class="step-flight_info-tag" role="status" aria-label="Trip type">
          <% if(_routeType===1) { %> One way <% } %> <% if(_routeType===2) { %> Round trip <% } %> <% if(_routeType===3)
          { %> Multi-city <% } %>
        </span>

        <% if(_routeType !== 3){ %>
        <div class="step-flight_info-text" role="group" aria-label="Flight route">
          <span id="departure-city">Beijing</span>
          <img class="step-flight_info-way_icon" src=<%=
          _routeType===2?"../../images/flightOptions/roundTrip.svg":"../../images/flightOptions/oneWay.svg" %>
          alt="Flight direction from Beijing to Guangzhou" role="img" />
          <span id="arrival-city">Guangzhou</span>
        </div>
        <% } %>
      </div>

      <% if(_routeType !== 3){ %>
      <div class="step-flight_info-bottom">
        <div class="step-flight_info-tag">
          <span role="status" id="step-flight-info-departure" aria-label="Departure date">Departure: 2025-06-14</span>
          <% if(_routeType===2){ %>
          <span role="status" aria-label="Return date">&nbsp;&nbsp;Return: 2025-06-15</span>
          <% } %>
        </div>

        <div class="step-flight_info-person" role="status" aria-label="Passenger information">
          Adult×1, Child×0, Infant×0
        </div>
      </div>
      <% } %>
    </div>

    <div class="wrap-button">
      <button
        class="__text-button"
        type="button"
        aria-label="Edit flight search criteria"
        aria-describedby="edit-search-description"
        onclick="modal.open()">
        <span id="edit-search-description">Edit search</span>
        <img
          class="__text-button-icon"
          src="../../images/flightOptions/arrow_full_left.svg"
          alt=""
          role="presentation"
          aria-hidden="true" />
      </button>
    </div>
  </div>

  <div>
    <%- include('../../components/sz-modal/index', {title: 'Re-query Flight', okText: 'Search'} ) %>

    <div id="modal-content-flight" class="modal-content-flight">
      <%- include('./form-tab/flights', { showType: 'modal' }) %>
    </div>
  </div>
</div>
