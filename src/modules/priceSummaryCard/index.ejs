<%
    const _passengers = [
      { "type": "Passenger1 Adult", "fare": 986.1, "taxes": 12.9 },
      { "type": "Passenger2 Adult", "fare": 986.1, "taxes": 12.9 }
    ]
%>

<div class="price-summary-card" role="region" aria-label="Price summary">
  <!-- Header with total price and decorative pattern -->
  <div class="price-header">
    <div class="decorative-pattern">
      <img src="../../images/totem.svg" alt="Decorative pattern" aria-hidden="true" />
    </div>
    <div class="header-content">
      <span class="total-label">Total price:</span>
      <span class="total-amount" aria-label="Total price CNY 2,118">CNY 2,118</span>
    </div>
  </div>

  <!-- Price breakdown body -->
  <div class="price-body">
    <% _passengers.forEach((passenger, index) => { %>
    <div class="passenger-section" role="group" aria-label="Passenger <%= index + 1 %> pricing details">
      <div class="passenger-header">
        <span class="passenger-name" role="heading" aria-level="3">Passenger <%= index + 1 %> Adult</span>
      </div>

      <div class="fare-row">
        <div class="fare-label">
          <span>Fare:</span>
        </div>
        <span class="fare-amount" aria-label="Fare CNY <%= passenger.fare %>">CNY <%= passenger.fare %></span>
      </div>

      <div class="taxes-row">
        <div class="taxes-label">
          <span>Taxes:</span>
          <div
            class="__text-button"
            role="button"
            tabindex="0"
            data-tippy-content="type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type"
            data-tippy-allowHTML="true">
            <span class="icon-zh icon-zh-ask" aria-hidden="true"></span>
          </div>
        </div>
        <span class="taxes-amount" aria-label="Taxes CNY 12.9">CNY 12.9</span>
      </div>

      <div class="passenger-total">
        <span class="total-label">total：</span>
        <span
          class="total-amount"
          aria-label="Passenger total CNY <%= (passenger.fare + 12.9).toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ',') %>">
          CNY <%= (passenger.fare + 12.9).toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ',') %>
        </span>
      </div>
    </div>

    <% if (index < _passengers.length - 1) { %>
    <div class="divider" role="separator" aria-hidden="true"></div>
    <% } %>
    <% }); %>
  </div>
</div>
