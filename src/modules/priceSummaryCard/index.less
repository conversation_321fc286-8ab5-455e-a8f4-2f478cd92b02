@import '../../less/variables.less';
@import '../../less/mediaMixin.less';

.price-summary-card {
  width: 450px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

  .screenPad({
    display: none;
  });

  .screenMobile({
    display: none;
  });

  .price-header {
    background: @brand-4;
    color: @gray-0;
    padding: 23px 20px;
    position: relative;
    border-radius: 8px 8px 0 0;

    .decorative-pattern {
      position: absolute;
      top: 0;
      left: 0;
      opacity: 0.3;
      pointer-events: none;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      z-index: 1;

      .total-label {
        font-family: 'Source Han <PERSON>', sans-serif;
        font-size: 24px; // 24px/24px-默认
        font-weight: 400;
        line-height: auto;
      }

      .total-amount {
        font-family: 'Source <PERSON>', sans-serif;
        font-size: 24px; // 24px/24px-默认
        font-weight: 400;
        line-height: auto;
      }
    }
  }

  .price-body {
    background: @gray-0;
    padding: 20px;
    border: 1px solid #cccccc;
    border-top: none;
    border-radius: 0 0 8px 8px;

    .passenger-section {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .passenger-header {
        margin-bottom: 10px;

        .passenger-name {
          font-family: 'Source Han Sans', sans-serif;
          font-size: 20px; // 20px/20px-粗
          font-weight: 500;
          color: @sub-4;
        }
      }

      .fare-row,
      .taxes-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        .fare-label,
        .taxes-label {
          display: flex;
          align-items: center;
          gap: 10px;

          span {
            font-family: 'Source Han Sans', sans-serif;
            font-size: 16px; // 16px/16px-默认
            font-weight: 400;
            color: @gray-3;
          }
        }

        .fare-amount,
        .taxes-amount {
          font-family: 'Source Han Sans', sans-serif;
          font-size: 16px; // 16px/16px-默认
          font-weight: 400;
          color: @sub-4;
        }
      }

      .passenger-total {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;

        .total-label {
          font-family: 'Source Han Sans', sans-serif;
          font-size: 20px; // 20px/20px-粗
          font-weight: 500;
          color: @sub-4;
        }

        .total-amount {
          font-family: 'Source Han Sans', sans-serif;
          font-size: 20px; // 20px/20px-粗
          font-weight: 500;
          color: @sub-4;
        }
      }
    }

    .divider {
      height: 1px;
      background: @gray-1;
      margin: 20px 0;
    }
  }
}
