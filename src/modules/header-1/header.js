// 非首页 header
// js 行为 借用 modules/header/header.js 代码

// 这两个变量是全局的，代表当前选择的国家和语言，可以存在cookie里，在config.js里获取到
var selectCountry = undefined;
var selectLanguage = undefined;

window.addEventListener('load', function (event) {
  const isFreshed = sessionStorage.getItem('refreshed');
  if (!isFreshed) {
    const label = languageTreeList.find(item => item.code === window.Locale?.split('_')[1])?.label || '';
    sessionStorage.setItem('beforeCountry', label);
  }
  sessionStorage.setItem('refreshed', 'true');
  toggleStates();
  // 检查并隐藏已被移除的 tip 区域
  checkAndHideRemovedTips();
});

/**
 * 移除 header tip
 */
const HEADER_TIP_REMOVED = 'header_tip_removed_';
const removeTip = domId => {
  const removeDom = document.getElementById(domId);
  if (removeDom) {
    removeDom.remove();
    // 在 localStorage 中存储标记，表示该区域已被移除
    localStorage.setItem(`${HEADER_TIP_REMOVED}${domId}`, 'true');
  }
};

/**
 * 检查并隐藏已被标记为移除的 tip 区域
 */
const checkAndHideRemovedTips = () => {
  // 遍历 localStorage 中所有以 'HEADER_TIP_REMOVED' 开头的键
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith(`${HEADER_TIP_REMOVED}`) && localStorage.getItem(key) === 'true') {
      const domId = key.replace(`${HEADER_TIP_REMOVED}`, '');
      const element = document.getElementById(domId);
      if (element) {
        element.style.display = 'none';
      }
    }
  }
};

// header 相关逻辑

// fix bug 修复header dropdown 无法在屏幕内满屏展开

// const toggleDom = document.getElementById('dropdown-toggle')
// const popperPlane = document.getElementById('pc-header-dropdown-content')
// const popperIns = Popper.createPopper(toggleDom, popperPlane, {
//   placement: 'bottom'
// })

// toggleDom.addEventListener("mousemove", popperIns.update)

// toggleDom.addEventListener("focus", popperIns.update)

const headerBtnListWrap = document.getElementsByClassName('btn-group');
if (headerBtnListWrap.length) {
  const btnList = headerBtnListWrap[0].querySelectorAll('.dropdown-toggle');
  btnList.forEach(btn => {
    const popperIns = Popper.createPopper(btn, btn.nextElementSibling, {
      placement: 'bottom',
    });

    btn.addEventListener('mousemove', popperIns.update);
    btn.addEventListener('focus', popperIns.update);
    // 进入 副菜单的时候保持主菜单的背景色
    btn.nextElementSibling &&
      btn.nextElementSibling.addEventListener('mouseenter', () => {
        $(btn).addClass('actived');
      });
    // 移出 副菜单的时候保持主菜单的背景色
    btn.nextElementSibling &&
      btn.nextElementSibling.addEventListener('mouseleave', () => {
        $(btn).removeClass('actived');
      });
  });
}

// mobile 行为
let mobilePlaneVisible = false;

function toggleMobilePlaneVisible() {
  mobilePlaneVisible = !mobilePlaneVisible;

  if (mobilePlaneVisible) {
    mobileAppHeaderContent.classList.remove('mobile-app-header-content-hidden');
  } else {
    mobileAppHeaderContent.classList.add('mobile-app-header-content-hidden');
  }
}

/**
 * @param {MouseEvent} event
 */
function stopPropagation(event) {
  event.stopPropagation();
}

/**
 * @param {HTMLDivElement} dom
 */
function toggleDirectory1(dom) {
  const isUnfold = dom.classList.contains('title-active');
  const directory1Dom = document.querySelectorAll('.directory-1');

  directory1Dom.forEach(item => {
    const titleDom = item.firstElementChild;
    titleDom.nextElementSibling.classList.add('directory-2-hidden');
    titleDom.classList.remove('title-active');
  });

  if (isUnfold) return;

  // 展开子集
  const sonPlaneDom = dom.nextElementSibling;

  if (isUnfold) {
    dom.classList.remove('title-active');
    sonPlaneDom.classList.add('directory-2-hidden');
  } else {
    dom.classList.add('title-active');
    sonPlaneDom.classList.remove('directory-2-hidden');
  }
}

/**
 * @param {HTMLDivElement} dom
 */
function toggleDirectory2(dom) {
  const isUnfold = dom.classList.contains('title-active');

  const allTitleDom = dom.parentElement.querySelectorAll('.title');
  allTitleDom.forEach(item => {
    item.classList.remove('title-active');
    item.nextElementSibling.classList.add('directory-3-hidden');
  });

  if (isUnfold) return;
  const sonPlaneDom = dom.nextElementSibling;
  if (isUnfold) {
    dom.classList.remove('title-active');
    sonPlaneDom.classList.add('directory-3-hidden');
  } else {
    dom.classList.add('title-active');
    sonPlaneDom.classList.remove('directory-3-hidden');
  }
}

let isLogin = false;

function toggleLogin() {
  isLogin = !isLogin;

  if (isLogin) {
    noLoginDom.style.visibility = 'hidden';
    noLoginDom.style.display = 'none';
    loginDom.style.display = 'block';
  } else {
    noLoginDom.style.visibility = 'visible';
    noLoginDom.style.display = 'flex';
    loginDom.style.display = 'none';
  }
}

const padToggleLogin = () => {
  isLogin = !isLogin;

  if (isLogin) {
    padNoLoginDom.style.visibility = 'hidden';
    padNoLoginDom.style.display = 'none';
    padLoginDom.style.display = 'block';
  } else {
    padNoLoginDom.style.visibility = 'visible';
    padNoLoginDom.style.display = 'flex';
    padLoginDom.style.display = 'none';
  }
};

const onLanguageKeydown = (event, dom) => {
  if (event.key === 'Enter') {
    toggleLanguage(dom);
  }
};

function toggleStates() {
  /**
   * @type {HTMLDivElement[]}
   */
  const containerList = Array.from(document.querySelectorAll("[data-role='countryWrap']"));

  // 子节点，需要配置的话，要单独把label写在arr里
  const htmlText = languageTreeList.map((item, index) => {
    return `<a href="javascript:" role="option" data-role="firstCountry"
              class="select-item ${languageTreeList[0].innerLabel.includes(item.label) ? 'child-item' : ''}"
              onclick="toggleCountry(this,true)">
                <img alt="${item.label}" src="${item.icon}">
                <p aria-label="${item.label}">${item.label}</p>
            </a>`;
  });

  containerList.forEach(container => {
    container.innerHTML = htmlText.join('');
    toggleCountry(container.firstChild);
  });
}

/**
 * @param {HTMLDivElement} dom
 * @param  isInit
 */
function toggleCountry(dom, isInit = false) {
  const beforeCountry = sessionStorage.getItem('beforeCountry');
  const beforeLanguage = sessionStorage.getItem('beforeLanguage');
  const parent = dom.parentElement;

  let hintDomIndex = 0;
  for (let i = 0; i < parent.children.length; i++) {
    parent.children[i].classList.remove('select-item-active');
    languageTreeList.forEach(item => (item.active = false));
    if (!isInit && beforeCountry && parent.children[i].children[1].textContent.trim() === beforeCountry) {
      hintDomIndex = i;
      parent.children[i].classList.add('select-item-active');
    } else {
      if (parent.children[i] === dom) hintDomIndex = i;
      if (isInit) dom.classList.add('select-item-active');
    }
  }
  languageTreeList[hintDomIndex].active = true;
  selectCountry = languageTreeList[hintDomIndex].label;
  sessionStorage.setItem('beforeCountry', languageTreeList[hintDomIndex].label);
  const htmlText = languageTreeList[hintDomIndex].children.map((item, index) => {
    item.active = false;
    return `<a class="select-item ${item.label === beforeLanguage ? 'select-item-active' : ''}"
                 href="javascript:" role="button"
                 onkeydown="onLanguageKeydown(event, this)"
                 onclick="toggleLanguage(this)">
                  <p>${item.label}</p>
            </a>`;
  });

  const selectLanguageWrapList = Array.from(document.querySelectorAll("[data-role='selectLanguageWrap']"));

  selectLanguageWrapList.forEach(selectLanguageWrap => {
    selectLanguageWrap.innerHTML = htmlText.join('');
  });
}

/**
 *
 * @param dom
 */
const onCountryKeyDown = dom => {
  const languageDomList = Array.from(selectLanguageWrap.querySelectorAll('.select-item'));
  if (languageDomList.length === 0) return;

  languageDomList[0].focus();
};

/**
 * 切换语言的函数
 * @param {HTMLDivElement} dom - 需要操作的 DOM 元素
 * @param {Function} [func=() => {}] - 回调函数，默认值为一个空函数
 */
function toggleLanguage(dom, func = () => {}) {
  const parent = dom.parentElement;
  let hintDomIndex = 0;

  const countryIndex = languageTreeList.findIndex(item => item.active);
  for (let i = 0; i < parent.children.length; i++) {
    parent.children[i].classList.remove('select-item-active');

    if (parent.children[i] === dom) hintDomIndex = i;
    languageTreeList[countryIndex].children.forEach(item => (item.active = false));
  }

  languageTreeList[countryIndex].children[hintDomIndex].active = true;
  dom.classList.add('select-item-active');

  const languageData = languageTreeList[countryIndex].children.find(item => item.active);
  selectLanguage = languageData.label;
  sessionStorage.setItem('beforeLanguage', languageData.label);

  Array.from(document.querySelectorAll("[data-role='countryImgDom']")).forEach(countryImgDom => {
    countryImgDom.src = languageTreeList[countryIndex].icon;
  });
  Array.from(document.querySelectorAll("[data-role='countryDom']")).forEach(countryDom => {
    countryDom.innerText = languageTreeList[countryIndex].label;
  });
  Array.from(document.querySelectorAll("[data-role='languageDom']")).forEach(languageDom => {
    if (languageData) languageDom.innerText = languageData.label;
  });

  func();
}

/* 处理 header more 点击后效果 */
/**
 * @param {HTMLDivElement} moreBtnDom
 */
const showMoreLink = moreBtnDom => {
  const infoWrapDom = moreBtnDom.parentElement;

  const linkListDom = infoWrapDom.querySelectorAll('.info-item');
  Array.from(linkListDom).forEach(link => (link.style.display = 'block'));
  moreBtnDom.remove();
};

const initHeaderMore = () => {
  const headerInfoWrapList = document.querySelectorAll('.pc-app-header-inner .info-wrap');

  headerInfoWrapList.forEach(infoWrap => {
    const linkWrap = infoWrap.getElementsByTagName('ul');
    const linkList = linkWrap[0] ? linkWrap[0].getElementsByTagName('li') : [];

    const hasMore = infoWrap.querySelector('.info-item.__text-button');

    Array.from(linkList).forEach((link, index) => {
      if (index < 5) return;
      link.style.display = 'none';
    });

    if (linkList.length > 5 && !hasMore) {
      /* 添加more */ infoWrap.insertAdjacentHTML(
        'beforeend',
        `
      <a href="javascript:" aria-label="More" onclick="showMoreLink(this)" role="button" class="info-item __text-button no-style">
          <span class="__text-button-label">More</span>
          <img class="__text-button-icon" alt="" src="../../images/arrow-right-red.svg">
      </a>
     `
      );
    }
  });
};

const initHeaderDropdownVisibleEvent = () => {
  const headerDropdownList = document.querySelectorAll('.dropdown-content.dropdown-content-header');

  headerDropdownList.forEach(headerDropdown => {
    const io = new IntersectionObserver(entries => {
      const visible = entries[0].isIntersecting;
      if (visible) {
        initHeaderMore();
      }
    }, {});

    io.observe(headerDropdown);
  });
};

initHeaderDropdownVisibleEvent();

// 移动端手机弹窗逻辑

/**
 * @type {HTMLDialogElement}
 */
const mobileLanguageDialog = document.getElementById('mobileLanguageDialog');
/**
 *
 * @type {HTMLInputElement}
 */
const countryInputDom = document.querySelector("[data-role='mobile-country-selector']");
/**
 *
 * @type {HTMLInputElement}
 */
const languageInputDom = document.querySelector("[data-role='mobile-language-selector']");

const mobileCountyChange = () => {
  // 当国家改变时, 置空语言选择
  languageInputDom.value = '';
};

/**
 * @param {HTMLInputElement} dom
 */
const mobileChooseLanguage = dom => {
  const country = countryInputDom.value;
  if (!country) return confirm('Please select the country and then select the language');

  const languageList = languageTreeList.find(item => item.label === country).children;
  dom.openDropdownSelect(languageList, {
    labelFiled: 'label',
    valueFiled: 'label',
  });
};

const submitLanguage = () => {
  if (!countryInputDom.value || !languageInputDom.value)
    return confirm('Please select the country and then select the language');

  const country = countryInputDom.value;
  const language = languageInputDom.value;

  selectCountry = country;
  selectLanguage = language;

  sessionStorage.setItem('beforeCountry', country);
  sessionStorage.setItem('beforeLanguage', language);

  // const imgDom = document.querySelector("[data-role='mobile-language-img']");
  // imgDom.src = languageTreeList.find(item => item.label === country).icon;
  //
  // const countryDom = document.querySelector("[data-role='mobile-county']");
  // countryDom.innerText = country;
  //
  // const languageDom = document.querySelector("[data-role='mobile-language']");
  // languageDom.innerText = language;

  mobileLanguageDialog.close();
};

const openMobileLanguageDialog = () => {
  if (mobileLanguageDialog) {
    mobileLanguageDialog.show();
  }
};

const closeMobileLanguageDialog = () => {
  if (mobileLanguageDialog) {
    mobileLanguageDialog.close();
  }
};
