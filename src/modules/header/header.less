@import '../../less/mediaMixin.less';

.dropdown {
  &:hover,
  &:focus-within {
    .dropdown-content {
      display: block;
    }

    .dropdown-content-header {
      display: flex;
    }
  }

  .dropdown-content {
    display: none;
    position: absolute;
    z-index: 1;

    .triangle {
      background-color: transparent;
      height: 0.75rem;
      width: 0.9rem;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 0.4rem;
        left: calc(50% - 0.25rem);
        transform: translateX(-50%) rotate(45deg);

        width: 0.9rem;
        height: 0.9rem;
        background-color: #ffffff;
      }
    }

    &-user {
      width: 6.85rem;

      &-login {
        width: 6.85rem;

        .dropdown-content-login {
          display: flex !important;
          align-items: center;

          .menu-item {
            padding-left: 1rem !important;
          }
        }

        .dropdown-content-inner {
          transform: translateX(-2.75rem);
          display: block;

          .menu-item {
            font-size: 0.7rem;
            line-height: 1rem;
            display: flex;
            align-items: center;
            color: #554e49;
            margin-bottom: 0.5rem;

            &:last-child {
              cursor: pointer;
              margin-bottom: 0;
              padding-left: 1.5rem;
            }

            img {
              margin-right: 0.5rem;
              width: 1rem;
              height: 1rem;
              object-fit: cover;
            }
          }
        }
      }

      .dropdown-content-inner {
        transform: translateX(-2.75rem);
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 0.7rem;
        color: #554e49;

        .text-btn {
          cursor: pointer;
          user-select: none;
          transition: 0.2s;

          &:hover {
            color: #cc0100;
          }
        }
      }
    }

    &-language {
      width: 20.5rem;
      transform: translateX(-12.5rem);

      .triangle {
        transform: translateX(17.5rem);

        .screenPad({
          transform : translateX(17rem);
        });
      }

      .dropdown-content-inner {
        padding: 1rem 0.5rem;
        box-sizing: border-box;
        display: flex;
        gap: 0.5rem;

        .select-area {
          flex: 1;
          flex-shrink: 0;

          .select-title {
            font-size: 0.9rem;
            line-height: 1rem;
            color: #101010;
            margin-bottom: 0.7rem;
            padding-left: 0.5rem;
          }
        }

        .select-area {
          .select-wrap {
            height: 7.55rem;
            padding: 0;
            overflow-y: auto;

            .select-item {
              display: flex;
              align-items: center;
              padding: 0.35rem 0.5rem;
              box-sizing: border-box;
              cursor: pointer;
              transition: 0.2s;
              margin-bottom: 0.25rem;
              border-radius: 0.2rem;
              width: 100%;

              &:last-child {
                margin-bottom: 0;
              }

              &:hover,
              &:focus-within {
                background-color: #f9e5e5;
              }

              &-active {
                background-color: #f9e5e5;
              }

              img {
                width: 0.9rem;
                height: 1rem;
                margin-right: 0.5rem;
                object-fit: cover;
              }

              p {
                font-size: 0.7rem;
                line-height: 1rem;
                color: #554e49;
              }
            }

            .child-item {
              padding-left: 2rem;
            }
          }
        }
      }
    }

    &-header {
      //transform           : translateX(-20.5rem);
      //position            : absolute;
      //top                 : 4rem;
      //left                : 0;
      width: 100vw;
      box-sizing: border-box;
      background-image: url('../images/header-airline-logo.png');
      background-repeat: no-repeat;
      background-position: calc(1%) 3.05rem;
      background-color: #ffffff;
      background-size: 15rem 16rem;

      &-inner {
        // margin     : 0 auto;
        display: flex;
        gap: 1.5rem;
        box-sizing: border-box;
        padding: 1rem 0 1rem 9.5rem;
        // min-width  : 1025px;
        flex-wrap: wrap;

        .info-wrap {
          width: 11rem;

          .info-title {
            font-weight: 400;
            font-size: 0.7rem;
            line-height: 0.95rem;
            color: #101010;
            padding-left: 0.4rem;
            border-left: 0.2rem solid #cc0100;
            // margin-bottom : 1rem;
          }

          .info-item {
            padding-left: 0.6rem;
            font-size: 0.7rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            cursor: pointer;

            &:first-child {
              padding-left: 0;
            }

            .more {
              color: #cc0100;
            }
          }
        }
      }
    }

    &-inner {
      z-index: 5;
      position: relative;
      background-color: #ffffff;
      padding: 0.5rem;
      border-radius: 0.2rem;
      box-shadow: 0 0.4rem 0.8rem 0 rgba(0, 0, 0, 0.2);
    }
  }
}

.pc-header-wrap {
  .cookie-close {
    vertical-align: top;
  }
}

@media screen and (min-width: 1440px) {
}

@media screen and (min-width: 1025px) /* PC 端 */ {
  .ipad-header-wrap,
  .mobile-app-header-content {
    display: none;
  }

  .pc-header-wrap {
    display: block;
    position: relative;
    top: 0;
    z-index: 25;
    width: 100%;

    .pc-header-nav {
      width: 100%;
      background: rgba(235, 213, 156, 0.8);

      .pc-header-nav-inner {
        margin: 0 auto;
        height: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .content {
          color: #101010;
          font-size: 0.7rem;
          flex: 1;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .nav-icon-close {
          width: 0.8rem;
          height: 0.8rem;
          margin-left: 0.85rem;
          cursor: pointer;
          flex-shrink: 0;
        }
      }
    }

    .pc-app-header {
      height: 5rem;
      background-color: #2c2420;
      box-sizing: border-box;

      &-inner {
        background-color: #2c2420;
        height: 4rem;
        margin: 0 auto;
        position: relative;

        .btn-group {
          display: flex;

          .header-dropdown-btn {
            margin-right: 1.5rem;
            height: 4rem;
            transform: translateY(1.45rem);

            .btn {
              margin-right: 0;
              transform: translateY(0);
            }
          }

          .logo {
            width: 7.5rem;
            height: 8.5rem;
            margin-right: 0.75rem;
            z-index: 100;

            img {
              width: 7.5rem;
              height: 8.5rem;
              object-fit: cover;
            }
          }

          .btn {
            // word-break      : break-all;
            padding: 0 0.75rem;
            color: #ffffff;
            cursor: pointer;
            user-select: none;
            height: 3.5rem;
            transition: 0.2s;
            font-size: 0.8rem;
            transform: translateY(1.45rem);
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1rem;
            margin-right: 1.5rem;

            &:hover {
              background-color: #cc0100;
            }

            &-active {
              background-color: #cc0100;
            }

            &.actived {
              background-color: #cc0100;
            }
          }
        }

        .right-area {
          position: absolute;
          right: 1.5rem;
          top: 0.5rem;

          display: flex;
          align-items: center;

          .tool-item {
            color: #ffffff;
            cursor: pointer;
            display: flex;
            align-items: center;
            font-size: 0.7rem;
            margin-left: 1.5rem;

            &-border {
              padding: 0;
              display: flex;
              align-items: center;
              margin-left: 0;
              transform: translateX(-2.5rem);

              img {
                width: 0.9rem;
                height: 0.9rem;
                margin: 0 0.5rem;
              }
            }

            .split {
              margin: 0 0.5rem;
            }

            .img-flag {
              margin-right: 0.5rem;
            }

            &-btn {
              color: #ffffff;
              font-size: 0.7rem;
              display: flex;
              align-items: center;

              .icon-zh-profile {
                margin-right: 0.5rem;
                width: 0.9rem;
                height: 0.9rem;
                font-size: 0.6rem;
                border-radius: 50%;
                border: 0.06rem solid #ffffff;
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }
          }
        }
      }
    }
  }
}
//非手机端
@media screen and (min-width: 767px) {
  .mobile-app-language-selector-wrapper {
    display: none;
  }
}

@media screen and (max-width: 1024px) /* IPAD */ {
  .pc-header-wrap {
    display: none;
  }

  .ipad-header-wrap {
    display: block;
    position: sticky;
    top: 0;
    z-index: 25;
    width: 100%;

    .ipad-header-nav {
      width: 100%;
      background: rgba(235, 213, 156, 0.8);

      .pc-header-nav-inner {
        margin: 0 auto;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .content {
          color: #101010;
          font-size: 0.7rem;
          flex: 1;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .nav-icon-close {
          width: 0.8rem;
          height: 0.8rem;
          margin-left: 0.85rem;
          cursor: pointer;
          flex-shrink: 0;
        }
      }
    }

    .mobile-app-header {
      height: 3.5rem;
      background-color: #2c2420;

      // width            : 100vw;
      z-index: 20;
      padding: 0 1.5rem;
      box-sizing: border-box;

      display: flex;
      align-items: center;
      justify-content: space-between;

      .btn-group {
        .logo {
          width: 4.85rem;
          height: 5.5rem;
          transform: translateY(0.95rem);
        }
      }

      .mobile-language-dropdown {
        display: none;
      }

      .right-area {
        display: flex;
        align-items: center;

        .pad-language-dropdown {
          display: block;
        }

        .tool-item {
          color: #ffffff;
          cursor: pointer;
          display: flex;
          align-items: center;
          font-size: 0.7rem;
          margin-left: 0.5rem;

          &-border {
            padding: 0;
            display: flex;
            align-items: center;
            margin-left: 0;
            // transform   : translateX(-2.5rem);

            img {
              width: 0.9rem;
              height: 0.9rem;
              margin: 0 0.5rem;
            }
          }

          .split {
            margin: 0 0.5rem;
          }

          .img-flag {
            margin-right: 0.5rem;
          }

          &-btn {
            color: #ffffff;
            font-size: 0.7rem;
            display: flex;
            align-items: center;

            .icon-zh-profile {
              margin-right: 0.5rem;
              width: 0.9rem;
              height: 0.9rem;
              font-size: 0.6rem;
              border-radius: 50%;
              border: 0.06rem solid #ffffff;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }
    }
  }

  .mobile-app-header-content {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 10;
    background-color: rgba(16, 16, 16, 0.8);
    display: block;

    &-hidden {
      height: 0;
      overflow: hidden;
      opacity: 0;
    }

    &-inner {
      max-height: 80vh;
      overflow: scroll;
      margin-top: 6.55rem;
      width: 100vw;
      background-color: #ffffff;
      z-index: 20;

      .directory-1 {
        border-bottom: 0.05rem solid #cccccc;

        &:last-child {
          border-bottom: none;
        }

        .title {
          box-sizing: border-box;
          padding: 1rem 1.2rem;
          font-size: 0.8rem;
          color: #101010;

          display: flex;
          align-items: center;
          justify-content: space-between;
          // font-weight     : 600;
          transition: 0.2s;

          .menu-select {
            transform: rotate(90deg);
            transition: 0.2s;
          }

          &-active {
            background-color: #cc0100;
            color: #ffffff;
            position: relative;

            &::before {
              content: '';
              position: absolute;
              left: 0.35rem;
              top: 50%;
              transform: translateY(-50%);
              width: 0.1rem;
              height: 3.25rem;
              background-color: #ffffff;
            }

            .menu-select {
              color: #ffffff;
              transform: rotate(270deg);
              filter: none;
            }
          }
        }

        .directory-2 {
          background-color: #f8f1e5;

          &-hidden {
            display: none;
          }

          .title {
            line-height: 1rem;
            padding: 0.75rem 1.2rem 0.75rem 2.2rem;
            background: #f8f1e5;
            font-size: 0.7rem;
            font-weight: normal;
            border-bottom: 0.05rem solid #ffffff;

            .menu-select {
              transform: rotate(90deg);
              transition: 0.2s;
            }

            &-active {
              color: #101010;

              .sub-menu-select {
                transform: rotate(270deg);
                filter: none;
              }
            }
          }

          .directory-3 {
            &-hidden {
              display: none;
            }

            .info-item {
              line-height: 1rem;
              font-size: 0.6rem;
              border-bottom: 0.05rem solid #ffffff;
              color: #554e49;
              .info-link {
                display: block;
                margin: 0.75rem 1rem 0.75rem 3.2rem;
              }
            }
          }
        }
      }

      .language-selector {
        display: none;
      }
    }
  }
}

@media screen and (max-width: 767px) /* mobile */ {
  .ipad-header-wrap {
    .ipad-header-nav {
      .pc-header-nav-inner {
        height: 1.5rem;
        width: calc(100% - 1.5rem);

        .content {
          font-size: 0.6rem;
        }
      }
    }

    .mobile-app-header {
      height: 2.65rem;
      background-color: #2c2420;

      // width            : 100vw;
      z-index: 20;
      padding: 0 0.75rem;

      .btn-group {
        .logo {
          width: 3.65rem;
          height: 4.05rem;
          transform: translateY(0.72rem);
        }
      }

      .right-area {
        .tool-item {
          color: #ffffff;
          margin-right: 0.5rem;
          cursor: pointer;
          font-size: 0.6rem;
          display: flex;
          align-items: center;

          .split {
            margin: 0 0.25rem;
          }

          .icon-country {
            margin-right: 0.5rem;
          }

          .icon-zh-profile {
            font-size: 0.55rem;
            margin-right: 0.25rem;
            width: 0.9rem;
            height: 0.9rem;
            border-radius: 50%;
            border: 0.05rem solid #ffffff;
            text-align: center;
            line-height: 0.9rem;
          }

          &:last-child {
            // margin-left  : 0;
            margin-right: 0;
          }
        }

        .mobile-language-dropdown {
          display: block;
        }
        .pad-language-dropdown {
          display: none;
        }
      }
    }
  }

  .mobile-app-header-content {
    .language-selector {
      margin: 0.5rem 0.5rem 2.25rem;

      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.8rem;

      img {
        width: 1rem;
        height: 1rem;
        margin-right: 0.5rem;
        object-fit: contain;
      }

      p {
        margin-bottom: 0;
      }

      .dividing-line {
        margin: 0 0.5rem;
      }
    }
  }

  .mobile-app-language-selector-wrapper {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 20;

    background: rgba(0, 0, 0, 0.4);
    outline: none;
    border: none;
    .mobile-app-language-selector-inner {
      width: calc(100vw - 3rem);
      margin: 7rem auto 0;
      background: #ffffff;
      border-radius: 0.4rem;
      overflow: hidden;

      .header {
        display: grid;
        grid-template-columns: 1fr 4fr 1fr;
        align-items: center;
        grid-template-areas: '. title close';
        background: #dbd7d4;
        height: 2.5rem;
        font-size: 0.8rem;
        padding: 0 1.25rem;
        box-sizing: border-box;

        .title {
          grid-area: title;
          text-align: center;
        }

        .icon-close {
          grid-area: close;
          display: block;
          width: 0.7rem;
          height: 0.7rem;
          justify-self: end;
        }
      }

      .content {
        padding: 1.5rem 1rem;
        box-sizing: border-box;

        .input-wrap {
          margin-bottom: 1rem;
        }
      }
    }
  }
}
