{"productZoneData": [{"imageSrc": "./../../images/middleImg/product-zone-img-1.jpg", "title": "Package Zone", "ariaLabel": "Make the perfect holiday trip with the best offers and explore the b life experience bestMake the perfect holiday trip with the best", "description": "Make the perfect holiday trip with the best offers and explore the b life experience bestMake the perfect holiday trip with th experience bestMake the perfect holiday trip with th experience bestMake the perfect holiday trip with th experience bestMake the perfect holiday trip with the best", "arrowIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Package Zone"}, {"imageSrc": "./../../images/middleImg/product-zone-img-2.jpg", "title": "Student Area Student AreaStudent Area", "ariaLabel": "Book tickets with a favorable code to stay at ease", "description": "Book tickets with a favorable code to stay at ease", "arrowIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Student Area Student AreaStudent Area"}, {"imageSrc": "./../../images/middleImg/product-zone-img-1.jpg", "title": "Package Zone", "ariaLabel": "Make the perfect holiday trip with the best offers and explore the b life experience bestMake the perfect holiday trip with the best", "description": "Make the perfect holiday trip with the best offers and explore the b life experience bestMake the perfect holiday trip with the best", "arrowIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Package Zone"}, {"imageSrc": "./../../images/middleImg/product-zone-img-2.jpg", "title": "Student Area Student AreaStudent Area", "ariaLabel": "Book tickets with a favorable code to stay at ease", "description": "Book tickets with a favorable code to stay at ease", "arrowIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Student Area Student AreaStudent Area"}, {"imageSrc": "./../../images/middleImg/product-zone-img-1.jpg", "title": "Package Zone", "ariaLabel": "Make the perfect holiday trip with the best offers and explore the b life experience bestMake the perfect holiday trip with the best", "description": "Make the perfect holiday trip with the best offers and explore the b life experience bestMake the perfect holiday trip with the best", "arrowIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Package Zone"}, {"imageSrc": "./../../images/middleImg/product-zone-img-2.jpg", "title": "Student Area Student AreaStudent Area", "ariaLabel": "Book tickets with a favorable code to stay at ease", "description": "Book tickets with a favorable code to stay at ease", "arrowIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Student Area Student AreaStudent Area"}], "travelRecommendationsData": [{"imgSrc": "./../../images/middleImg/travel-recommend-img-1.jpg", "alt": "Doha", "place": "Doha", "description": "Doha Airport - Hamad International Airport (DOH)Doha Airport - Hamad International Airport (DOH)Doha Airport - Hamad International Airport (DOH)", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "moreIconAlt": "learn"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-2.jpg", "alt": "London", "place": "London", "description": "London Airport - Heathrow Airport (LHR)", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "moreIconAlt": "learn more"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-3.jpg", "alt": "Seoul", "place": "Seoul", "description": "Seoul Airport - Incheon International Airport (ICN)", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "moreIconAlt": "learn more"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-4.jpg", "alt": "Bangkok", "place": "Bangkok", "description": "Bangkok Airport - Suvannap International Airport (BKK)", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "moreIconAlt": "learn more"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-1.jpg", "alt": "Doha", "place": "Doha", "description": "Doha Airport - Hamad International Airport (DOH)", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "moreIconAlt": "learn more"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-2.jpg", "alt": "London", "place": "London", "description": "London Airport - Heathrow Airport (LHR)", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "moreIconAlt": "learn more"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-3.jpg", "alt": "Seoul", "place": "Seoul", "description": "Seoul Airport - Incheon International Airport (ICN)", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "moreIconAlt": "learn more"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-4.jpg", "alt": "Bangkok", "place": "Bangkok", "description": "Bangkok Airport - Suvannap International Airport (BKK)", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "moreIconAlt": "learn more"}], "travelRecommendationsMobileData": [{"imgSrc": "./../../images/middleImg/travel-recommend-img-1.jpg", "alt": "Doha", "place": "Doha"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-2.jpg", "alt": "London", "place": "London"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-3.jpg", "alt": "Seoul", "place": "Seoul"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-4.jpg", "alt": "Bangkok", "place": "Bangkok"}], "productRecommendationData": [{"imgSrc": "./../../images/middleImg/product-recommend-1.jpg", "title": "Preferred seat", "description": "Every distance you want to go, I accompany you, prepaid seat selection, your favorite seat 'lock' in advancselection, your favorite seat 'lock' in advancselection, your favorite seat 'lock' in advancselection, your favorite seat 'lock' in advance!", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Preferred seat"}, {"imgSrc": "./../../images/middleImg/product-recommend-2.jpg", "title": "Special meals", "description": "A Star Alliance ticket, an unlimited journey.", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Special meals"}, {"imgSrc": "./../../images/middleImg/product-recommend-3.jpg", "title": "Extra Baggage", "description": "Price surprise, excess value 8% off Easy shipping and saving time and heart saving", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Extra Baggage"}, {"imgSrc": "./../../images/middleImg/product-recommend-4.jpg", "title": "Hotel Transit", "description": "Become a member of Star Alliance Gold Card and enjoy exclusive courtesy.", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Hotel Transit"}], "eventRecommendationData": [{"imgSrc": "./../../images/middleImg/event-recommendation-img-1.jpg", "title": "Flying Takers ChallengeFlying Takers ChallengeFlying Takers ChallengeFlying Takers Challenge", "description": "Fly the sky, challenge unlimited! Wait for you to conquer the blue sky limit!", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Flying Takers Challenge"}, {"imgSrc": "./../../images/middleImg/event-recommendation-img-2.jpg", "title": "Elevator coupon", "description": "Upgrade experience, start with a booster ticket!", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Elevator coupon"}, {"imgSrc": "./../../images/middleImg/event-recommendation-img-3.jpg", "title": "Transfer accommodation", "description": "A warm resting station on the journey!", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Transfer accommodation"}, {"imgSrc": "./../../images/middleImg/event-recommendation-img-1.jpg", "title": "Transfer accommodation", "description": "A warm resting station on the journey!", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Transfer accommodation"}, {"imgSrc": "./../../images/middleImg/event-recommendation-img-2.jpg", "title": "Transfer accommodation", "description": "A warm resting station on the journey!", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Transfer accommodation"}, {"imgSrc": "./../../images/middleImg/event-recommendation-img-3.jpg", "title": "Transfer accommodation", "description": "A warm resting station on the journey!", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Transfer accommodation"}], "sevenDayCalendarData": [{"outbound": "06-20", "outboundDay": "<PERSON><PERSON>", "inbound": {"06-22": "-", "06-23": "-", "06-24": "-", "06-25": "-", "06-26": "920", "06-27": "920", "06-28": "920"}}, {"outbound": "06-21", "outboundDay": "Sat", "inbound": {"06-22": "-", "06-23": "-", "06-24": "-", "06-25": "-", "06-26": "920", "06-27": "920", "06-28": "1,920"}}, {"outbound": "06-22", "outboundDay": "Sun", "inbound": {"06-22": "-", "06-23": "1,920", "06-24": "1,920", "06-25": "-", "06-26": "920", "06-27": "920", "06-28": "1,920"}}, {"outbound": "06-23", "outboundDay": "Mon", "inbound": {"06-22": "-", "06-23": "1,920", "06-24": "-", "06-25": "-", "06-26": "920", "06-27": "920", "06-28": "1,920"}}, {"outbound": "06-24", "outboundDay": "<PERSON><PERSON>", "inbound": {"06-22": "-", "06-23": "-", "06-24": "1,920", "06-25": "-", "06-26": "920", "06-27": "920", "06-28": "1,920"}}, {"outbound": "06-25", "outboundDay": "Wed", "inbound": {"06-22": "-", "06-23": "-", "06-24": "-", "06-25": "-", "06-26": "1,920", "06-27": "1,920", "06-28": "1,920"}}, {"outbound": "06-26", "outboundDay": "<PERSON>hu", "inbound": {"06-22": "-", "06-23": "-", "06-24": "-", "06-25": "-", "06-26": "1,920", "06-27": "1,920", "06-28": "1,920"}}], "flightData": {"departureTime": "12:30", "departureAirport": "Shenzhen Bao'an International Airport", "arrivalTime": "14:35", "arrivalAirport": "Beijing Capital International Airport", "duration": "2h 5min", "flightNumber": "ZH8034", "aircraft": "Airbus A320", "sharing": true, "mealAvailable": true, "flightDetailsLink": "#", "priceOptions": [{"class": "Economy Class", "price": "13,999"}, {"class": "Premium Economy", "price": "13,999"}, {"class": "Business Class", "price": "13,999"}], "bookingClasses": [{"name": "Business Travel Economy Class", "remaining": 2, "price": "7,999", "taxesAndFees": "7,099", "fare": "900", "cabinClass": "U+A", "baggage": "1 Pieces", "refund": "300+", "change": "300+", "baseMileage": "+1142", "bonusMileage": "+1142", "type": "economy"}, {"name": "Eco Super Value", "remaining": 2, "price": "7,999", "taxesAndFees": "7,099", "fare": "900", "cabinClass": "U+A", "baggage": "1 Pieces", "refund": "300+", "change": "300+", "baseMileage": "+1142", "bonusMileage": "+1142", "type": "economy"}, {"name": "Business Travel Economy Class", "remaining": 2, "price": "7,999", "taxesAndFees": "7,099", "fare": "900", "cabinClass": "U+A", "baggage": "1 Pieces", "refund": "300+", "change": "300+", "baseMileage": "+1142", "bonusMileage": "+1142", "type": "premium-economy"}, {"name": "Eco Super Value", "remaining": 2, "price": "7,999", "taxesAndFees": "7,099", "fare": "900", "cabinClass": "U+A", "baggage": "1 Pieces", "refund": "300+", "change": "300+", "baseMileage": "+1142", "bonusMileage": "+1142", "type": "business"}]}, "flightOverview": {"flightData": {"tripType": "One Way", "classType": "Business Premium", "departure": {"time": "10:30", "date": "2025-07-01", "airport": "shenzhen international airport T1"}, "arrival": {"time": "13:30", "date": "2025-07-01", "airport": "Beijing Capital Airport T1"}, "flightNumber": "ZH8034", "aircraft": "Boeing 738", "shared": "共享", "carrier": "Shenzhen Airlines", "duration": "3h 0min", "meal": "<PERSON><PERSON>", "businessClass": "Business class/J class"}, "additionalInfo": [{"label": "Luggage", "value": "1件"}, {"label": "Base Mileage", "value": "4000(FYI)"}, {"label": "Bonus Mileage", "value": "4000(FYI)"}], "notes": ["The time of departure and arrival both refers to local time. Since there is likely a time difference between the place of departure and the destination, please properly arrange your schedule.", "We will reserve the seat(s) for you after the order is paid. Please make the payment as soon as the order is generated."], "policyTables": [{"title": "Ticket Refund", "groups": [{"title": "Before the flight takes off", "items": [{"type": "All unused", "price": "CNY600.00", "priceClass": "price"}, {"type": "partial use", "price": "CNY400.00", "priceClass": "price"}]}, {"title": "After the flight takes off", "items": [{"type": "All unused", "price": "CNY500.00", "priceClass": "price"}, {"type": "partial use", "price": "CNY300.00", "priceClass": "price"}]}]}, {"title": "Changes", "groups": [{"title": "Before the flight takes off", "items": [{"type": "All unused", "price": "free", "priceClass": "free"}, {"type": "partial use", "price": "free", "priceClass": "free"}]}, {"title": "After the flight takes off", "items": [{"type": "All unused", "price": "free", "priceClass": "free"}, {"type": "partial use", "price": "free", "priceClass": "free"}]}]}], "policyNotes": ["Where the refund and rebook rules are inconsistent in several flight segments, the strictest standard among them takes the prevail.", "Where there is a price difference between seats in the same class, seat changing shall make up the difference; where rescheduling and upgrade fees co-occur, both shall be charged together.", "A passenger must make a cancellation request within one year from the date of commencement of first travel (or from the date of ticket issuance with the first segment of voyage unused). Should this time period be exceeded, the request will not be processed. If ticket refund is not permitted, fuel surcharge and other fees will not be refunded."]}, "payment": {"flightSummary": {"departureCity": "<PERSON>", "arrivalCity": "<PERSON><PERSON>", "departureTime": "05:10", "arrivalTime": "19:30", "cabinClass": "Economy Class", "fareRule": "Economy Class", "refundRule": "Details", "changeRule": "Details", "baggageRule": "Details", "additionalInfo": "When the returns, change and reschedule rules for multiple flight segments differ, the strictest standard will be applied. For example, if you change a ticket with one non-refundable segment and one refundable segment, the non-refundable segment's rules apply to your entire ticket. Therefore, it is important to carefully review all rules before purchasing or making changes. Please consult the specific fare rules for details."}, "passengers": [{"name": "<PERSON>", "idType": "ID Card", "idNumber": "340xxxxxxxxxxxxx24", "nationality": "China", "birthday": "1997-07-03", "ticketNumber": "1314xxxxxxxxx", "contactPhone": "135xxxxxxx6", "contactEmail": "<EMAIL>", "baggageWeight": "N/A", "mealPreference": "N/A", "remark": "N/A"}], "additionalServices": {"seatSelection": [{"flight": "ZH9939", "status": "Selected: 11A", "price": "CNY 50.00"}, {"flight": "ZH9940", "status": "Selected: 12B", "price": "CNY 50.00"}, {"flight": "ZH9941", "status": "Selected: 13C", "price": "CNY 50.00"}], "specialLuggage": {"status": "No special luggage selected", "price": "CNY 0.00"}, "prepaidInsurance": {"status": "No prepaid insurance selected", "price": "CNY 0.00"}}, "contactInfo": {"contactName": "<PERSON>", "contactPhone": "***********", "contactEmail": "<EMAIL>", "invoice": "Not requested", "invoiceEmail": "N/A"}, "paymentMethods": [{"id": "qr_code", "name": "QR Code Payment", "active": true}, {"id": "international_card", "name": "International Card Payment"}, {"id": "credit_card", "name": "Credit Card Payment"}, {"id": "bank_card", "name": "Bank Card Payment"}, {"id": "third_party", "name": "Third Party Payment"}], "collectItinerary": {"options": [{"text": "Electronic itinerary (recommended): Can be printed directly, no procedure with ticket collection is necessary."}, {"text": "Paper itinerary: You can go to the airport counter to print and pick it up with your valid travel document. Please collect the itinerary within 7 days from the issuance date."}, {"text": "Home delivery itinerary: Express delivery of itinerary to your specified address. Delivery cost is CNY 20, payable upon delivery."}]}, "totalCost": "CNY 10,696.00"}}