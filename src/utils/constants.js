/**
 * 屏幕断点常量
 * 与 src/less/mediaMixin.less 中的媒体查询断点保持一致
 */
window.SCREEN_BREAKPOINTS = {
  // 移动端: 0-767px
  MOBILE_MAX: 767,

  // Pad端: 768-1024px
  PAD_MIN: 768,
  PAD_MAX: 1024,

  // 小屏PC: 1025-1440px
  SMALL_PC_MIN: 1025,
  SMALL_PC_MAX: 1440,

  // 大屏PC: >1440px
  LARGE_PC_MIN: 1440,
}

/**
 * 设备类型枚举
 */
window.DEVICE_TYPES = {
  MOBILE: 'mobile',
  PAD: 'pad',
  SMALL_PC: 'small_pc',
  LARGE_PC: 'large_pc',
}

/**
 * 根据屏幕宽度获取设备类型
 * @param {number} screenWidth - 屏幕宽度
 * @returns {string} 设备类型
 */
window.getDeviceType = function (screenWidth) {
  screenWidth = screenWidth || window.innerWidth
  if (screenWidth <= window.SCREEN_BREAKPOINTS.MOBILE_MAX) {
    return window.DEVICE_TYPES.MOBILE
  } else if (
    screenWidth >= window.SCREEN_BREAKPOINTS.PAD_MIN &&
    screenWidth <= window.SCREEN_BREAKPOINTS.PAD_MAX
  ) {
    return window.DEVICE_TYPES.PAD
  } else if (
    screenWidth >= window.SCREEN_BREAKPOINTS.SMALL_PC_MIN &&
    screenWidth <= window.SCREEN_BREAKPOINTS.SMALL_PC_MAX
  ) {
    return window.DEVICE_TYPES.SMALL_PC
  } else {
    return window.DEVICE_TYPES.LARGE_PC
  }
}

/**
 * 判断是否为移动设备（移动端或Pad端）
 * @param {number} screenWidth - 屏幕宽度
 * @returns {boolean}
 */
window.isMobileDevice = function (screenWidth) {
  screenWidth = screenWidth || window.innerWidth
  return screenWidth <= window.SCREEN_BREAKPOINTS.PAD_MAX
}

/**
 * 判断是否为PC设备（小屏PC或大屏PC）
 * @param {number} screenWidth - 屏幕宽度
 * @returns {boolean}
 */
window.isPCDevice = function (screenWidth) {
  screenWidth = screenWidth || window.innerWidth
  return screenWidth > window.SCREEN_BREAKPOINTS.PAD_MAX
}
