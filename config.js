const src = './src';
const build = './build';
const dist = './dist';
const fs = require('fs');
const path = require('path');

// 通用函数：根据路径和前缀生成第一层目录的通配符配置
function generateDirConfig(targetPath, prefix, filePattern = '**/*.js') {
  const configMap = {};

  if (!fs.existsSync(targetPath)) return configMap;

  // 只读取第一层目录
  const firstLevelDirs = fs.readdirSync(targetPath);

  firstLevelDirs.forEach(dirName => {
    const dirPath = path.join(targetPath, dirName);
    const stat = fs.statSync(dirPath);

    // 只处理目录
    if (stat.isDirectory()) {
      const key = `${prefix ? `${prefix}-` : ''}${dirName}`;

      // 生成相对于项目根目录的路径
      const relativePath = path.relative(__dirname, dirPath).replace(/\\/g, '/');
      configMap[key] = [`${relativePath}/${filePattern}`];
    }
  });

  return configMap;
}

module.exports = {
  browsersync: {
    development: {
      browser: ['google chrome'], // 'firefox' 可以设置多个浏览器打开
      server: {
        baseDir: [build],
        directory: true,
        // index:'03-1-1-国内机票预订-搜索结果-单程.html'
      },
      port: 11000,
    },
  },
  autoprefixer: {
    browsers: [
      'last 2 versions',
      // 浏览器前缀得看gulp-less插件,暂时配置last 2 versions
      // 'safari 12',
      // 'ie 11',
      // 'ios 10',
      // 'android 9'
    ],
    cascade: true,
  },
  src: {
    url: src,
    less: {
      main: [
        src + '/less/common.less',
        src + '/pages/home/<USER>',
        src + '/pages/sibo/sibo.less',
        src + '/pages/flightOptions/flightOptions.less', // 航班选择 less
        src + '/pages/flightOverview/flightOverview.less', // 航班概况 less
        src + '/pages/fillPsg/fillPsg.less', // 填写乘机人 less
        src + '/pages/additionalServices/additionalServices.less', // 附加服务 less
        src + '/pages/validateRouteInfo/validateRouteInfo.less', // 国际航线信息校验 less
        src + '/pages/payment/payment.less', // 支付 less
        src + '/pages/paymentResult/paymentResult.less', // 支付结果 less
      ],
      files: [
        src + '/less/**/*.less', // 主less
        src + '/modules/**/*.less', // 业务组件less
        src + '/components/**/*.less', // UI组件less
        src + '/plugins/**/*.less', // 自定义插件less
        src + '/pages/**/*.less', // pages less
      ],
    },
    ejs: {
      files: src + '/**/**/*.html',
    },
    pages_ejs: {
      files: src + '/pages/**/*.ejs',
    },
    data: {
      files: src + '/data',
    },
    modules: {
      tpl: src + '/modules/**/*.ejs',
    },
    js: {
      allFiles: [
        src + '/js/**/*.js',
        src + '/modules/**/*.js',
        src + '/components/**/*.js',
        src + '/pages/**/*.js',
        src + '/utils/**/*.js',
      ],
      common: [
        src + '/js/**/*.js', // 主js
      ],
      utils: [
        src + '/utils/**/*.js', // 主js
      ],

      // 动态生成的 components 键值对
      ...generateDirConfig(path.join(__dirname, src, 'components'), 'components'),

      // 动态生成的 modules 键值对
      ...generateDirConfig(path.join(__dirname, src, 'modules'), 'modules'),

      // 动态生成的 pages 键值对
      ...generateDirConfig(path.join(__dirname, src, 'pages')),
    },
    plugins: {
      // files: src + '/plugins/**/*'
      files: [src + '/plugins/**/*.js', src + '/plugins/**/*.css'],
    },
    icon: {
      files: src + '/fonts/**/*',
    },
    img: {
      url: src + '/images',
      files: src + '/images/**/*',
    },
    config: {
      files: src + '/config/**/*.js',
    },
  },
  build: {
    url: build,
    css: {
      url: build + '/css',
      files: build + '/css/**/*.css',
    },
    js: {
      url: build + '/js',
      files: build + '/js/**/*',
    },
    plugins: {
      url: build + '/plugins',
      files: build + '/plugins/**/*',
    },
    img: {
      url: build + '/images',
      files: build + '/images/**/*',
    },
    icon: {
      url: build + '/css/fonts',
      files: build + '/css/fonts/**/*',
    },
  },
  dist: {
    url: dist,
    css: {
      url: dist + '/css',
    },
    js: {
      url: dist + '/js',
    },
    plugins: {
      url: dist + '/plugins',
    },
    icon: {
      url: dist + '/css/fonts',
    },
    img: {
      url: dist + '/images',
    },
  },
};
